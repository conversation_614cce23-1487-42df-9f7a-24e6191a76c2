<div class="admin-dashboard-container">
  <div class="container">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1 class="dashboard-title">
          <span class="custom-icon">👨‍💼</span>
          Tableau de bord Admin
        </h1>
        <p class="dashboard-subtitle">
          Gestion des rendez-vous et affectation des infirmiers
          <span *ngIf="currentUser" class="user-info">
            - Connecté en tant que {{ currentUser.firstName }} {{ currentUser.lastName }}
          </span>
        </p>
      </div>
      <!-- Navigation entre vues -->
      <div class="view-navigation">
        <button
          class="nav-btn"
          [class.active]="currentView === 'appointments'"
          (click)="switchToAppointmentsView()">
          📅 Rendez-vous
        </button>
        <button
          class="nav-btn"
          [class.active]="currentView === 'patients'"
          (click)="switchToPatientsView()">
          👥 Patients
        </button>
        <button
          class="nav-btn"
          [class.active]="currentView === 'nurses'"
          (click)="switchToNursesView()">
          👩‍⚕️ Infirmiers
        </button>
        <button
          class="nav-btn"
          [class.active]="currentView === 'analyses'"
          (click)="switchToAnalysesView()">
          🧪 Analyses
        </button>
        <button
          class="nav-btn"
          [class.active]="currentView === 'statistics'"
          (click)="setCurrentView('statistics')">
          📊 Statistiques
        </button>
        <button
          class="nav-btn"
          [class.active]="currentView === 'tracking'"
          (click)="setCurrentView('tracking')">
          🗺️ Suivi Temps Réel
        </button>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <button class="action-btn" (click)="refreshData()" [disabled]="isLoading">
        🔄 {{ isLoading ? 'Actualisation...' : 'Actualiser' }}
      </button>

      <button
        class="action-btn primary"
        (click)="autoAssignAll()"
        [disabled]="isLoading || pendingAppointments.length === 0 || !canAssignNurses()"
        [title]="!canAssignNurses() ? 'Vous n\'avez pas les permissions pour assigner des infirmiers' : ''"
        *ngIf="currentView === 'appointments' && pendingAppointments.length > 0">
        🤖 Auto-assigner tout ({{ pendingAppointments.length }})
      </button>
    </div>

    <!-- Alerte de session -->
    <div *ngIf="sessionWarning" class="session-warning">
      ⚠️ Votre session expire bientôt. Veuillez sauvegarder votre travail.
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">⏳</div>
        <div class="stat-content">
          <div class="stat-number">{{ pendingCount }}</div>
          <div class="stat-label">En attente</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ confirmedCount }}</div>
          <div class="stat-label">Confirmés</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">👩‍⚕️</div>
        <div class="stat-content">
          <div class="stat-number">{{ availableNurses.length }}</div>
          <div class="stat-label">Infirmiers disponibles</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <div class="stat-number">{{ allPatients.length }}</div>
          <div class="stat-label">Patients</div>
        </div>
      </div>
    </div>

    <!-- Vue Patients -->
    <div *ngIf="currentView === 'patients'" class="patients-section">
      <!-- Liste des patients -->
      <div *ngIf="!selectedPatient" class="patients-list-view">
        <h2 class="section-title">
          👥 Liste des Patients
          <span class="count-badge">{{ allPatients.length }}</span>
        </h2>

        <div class="patients-grid" *ngIf="allPatients.length > 0">
          <div *ngFor="let patient of allPatients" class="patient-card" (click)="selectPatient(patient)">
            <div class="patient-card-content">
              <!-- Section gauche : Avatar et infos principales -->
              <div class="patient-main-info">
                <div class="patient-avatar">
                  {{ patient.firstName.charAt(0) }}{{ patient.lastName.charAt(0) }}
                </div>
                <div class="patient-basic-info">
                  <h3 class="patient-name">{{ patient.firstName }} {{ patient.lastName }}</h3>
                  <p class="patient-email">{{ patient.email }}</p>
                </div>
              </div>

              <!-- Section centre : Détails de contact -->
              <div class="patient-contact-info">
                <div class="info-row">
                  <span class="info-icon">📞</span>
                  <span class="info-text">{{ patient.phone || 'Non renseigné' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-icon">📍</span>
                  <span class="info-text">{{ patient.address || 'Non renseigné' }}</span>
                </div>
              </div>

              <!-- Section droite : Stats et actions -->
              <div class="patient-stats-actions">
                <div class="patient-stats">
                  <div class="stat-item">
                    <div class="stat-number">{{ getPatientAppointmentCount(patient.id!) }}</div>
                    <div class="stat-label">RDV total</div>
                  </div>
                  <div class="stat-item urgent" *ngIf="getPatientPendingAppointmentCount(patient.id!) > 0">
                    <div class="stat-number">{{ getPatientPendingAppointmentCount(patient.id!) }}</div>
                    <div class="stat-label">En attente</div>
                  </div>
                </div>

                <div class="patient-actions">
                  <button class="view-btn">
                    👁️ Voir les RDV
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="allPatients.length === 0" class="empty-state">
          <div class="empty-icon">👥</div>
          <h3>Aucun patient trouvé</h3>
          <p>Il n'y a actuellement aucun patient dans le système.</p>
        </div>
      </div>

      <!-- Détails du patient sélectionné -->
      <div *ngIf="selectedPatient" class="patient-details-view">
        <div class="patient-header">
          <button mat-icon-button (click)="backToPatientsList()" class="back-button">
            <mat-icon>arrow_back</mat-icon>
          </button>
          <div class="patient-info">
            <div class="patient-avatar-large">
              {{ selectedPatient.firstName.charAt(0) }}{{ selectedPatient.lastName.charAt(0) }}
            </div>
            <div class="patient-details">
              <h2>{{ selectedPatient.firstName }} {{ selectedPatient.lastName }}</h2>
              <p class="patient-email">{{ selectedPatient.email }}</p>
              <div class="patient-contact">
                <span *ngIf="selectedPatient.phone">
                  <mat-icon>phone</mat-icon>
                  {{ selectedPatient.phone }}
                </span>
                <span *ngIf="selectedPatient.address">
                  <mat-icon>location_on</mat-icon>
                  {{ selectedPatient.address }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <h3 class="appointments-title">
          📅 Rendez-vous de {{ selectedPatient.firstName }}
          <span class="count-badge">{{ selectedPatientAppointments.length }}</span>
        </h3>

        <div *ngIf="selectedPatientAppointments.length > 0" class="appointments-list">
          <div
            *ngFor="let appointment of selectedPatientAppointments"
            class="appointment-card"
            [class.urgent]="appointment.isUrgent"
          >
            <!-- Card Header -->
            <div class="card-header">
              <div class="appointment-info">
                <h3 class="appointment-title">
                  Rendez-vous #{{ appointment.id }}
                  
                  <span *ngIf="appointment.isUrgent" class="urgent-badge">🚨 URGENT</span>
                </h3>
                <div class="appointment-date">
                  📅 {{ formatDateTime(appointment.scheduledDate) }}
                </div>
              </div>
              <div class="status-badge" [ngClass]="getStatusBadgeClass(appointment.status)">
                {{ getStatusLabel(appointment.status) }}
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-section">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  [style.width.%]="getProgressPercentage(appointment.status)"
                ></div>
              </div>
              <div class="progress-steps">
                <div class="step" [class.active]="getStepStatus(appointment.status, 1)">
                  <div class="step-icon">📝</div>
                  <div class="step-label">Demandé</div>
                </div>
                <div class="step" [class.active]="getStepStatus(appointment.status, 2)">
                  <div class="step-icon">👩‍⚕️</div>
                  <div class="step-label">Assigné</div>
                </div>
                <div class="step" [class.active]="getStepStatus(appointment.status, 3)">
                  <div class="step-icon">🏠</div>
                  <div class="step-label">Prélèvement</div>
                </div>
                <div class="step" [class.active]="getStepStatus(appointment.status, 4)">
                  <div class="step-icon">🔬</div>
                  <div class="step-label">Analyse</div>
                </div>
                <div class="step" [class.active]="getStepStatus(appointment.status, 5)">
                  <div class="step-icon">📊</div>
                  <div class="step-label">Résultats</div>
                </div>
              </div>
            </div>

            <!-- Card Body -->
            <div class="card-body">
              <div class="appointment-details">
                <div class="detail-item">
                  <span class="detail-label">📝 Analyses :</span>
                  <span class="detail-value">
                    <span *ngFor="let analysis of appointment.analysisTypes; let last = last">
                      {{ analysis.name }}<span *ngIf="!last">, </span>
                    </span>
                  </span>
                </div>

                <div class="detail-item">
                  <span class="detail-label">📍 Adresse :</span>
                  <span class="detail-value">{{ appointment.homeAddress }}</span>
                </div>

                <div class="detail-item" *ngIf="appointment.specialInstructions">
                  <span class="detail-label">📝 Instructions :</span>
                  <span class="detail-value">{{ appointment.specialInstructions }}</span>
                </div>

                <div class="detail-item" *ngIf="appointment.nurse">
                  <span class="detail-label">👩‍⚕️ Infirmier(e) :</span>
                  <span class="detail-value">{{ appointment.nurse.firstName }} {{ appointment.nurse.lastName }}</span>
                </div>

                <div class="detail-item" *ngIf="appointment.symptoms">
                  <span class="detail-label">🩺 Symptômes :</span>
                  <span class="detail-value">{{ appointment.symptoms }}</span>
                </div>
              </div>

              <!-- Actions Admin -->
              <div class="admin-actions" *ngIf="appointment.status === 'PENDING' && canAssignNurses()">
                <button class="action-btn primary" (click)="openNurseSelectionModal(appointment)">
                  👩‍⚕️ Choisir un infirmier
                </button>
                <button class="action-btn secondary" (click)="autoAssignNurse(appointment)">
                  🤖 Auto-assigner
                </button>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="selectedPatientAppointments.length === 0" class="empty-state">
          <mat-icon>event_busy</mat-icon>
          <h3>Aucun rendez-vous</h3>
          <p>Ce patient n'a pas encore de rendez-vous programmé.</p>
        </div>
      </div>
    </div>

    <!-- Vue Infirmiers -->
    <div *ngIf="currentView === 'nurses'" class="nurses-section">
      <!-- Liste des infirmiers -->
      <div *ngIf="!selectedNurseForManagement && !showAddNurseForm" class="nurses-list-view">
        <div class="section-header">
          <h2 class="section-title">
            👩‍⚕️ Liste des Infirmiers
            <span class="count-badge">{{ allNurses.length }}</span>
          </h2>
          <button
            mat-raised-button
            color="primary"
            (click)="showAddNurseFormToggle()"
            *ngIf="canManageUsers()">
            ➕ Ajouter un Infirmier
          </button>
        </div>

        <div *ngIf="isLoading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Chargement des infirmiers...</p>
        </div>

        <div *ngIf="!isLoading && allNurses.length === 0" class="empty-state">
          <div class="empty-icon">👩‍⚕️</div>
          <h3>Aucun infirmier trouvé</h3>
          <p>Commencez par ajouter des infirmiers à votre équipe.</p>
        </div>

        <div *ngIf="!isLoading && allNurses.length > 0" class="nurses-grid">
          <div
            *ngFor="let nurse of allNurses"
            class="nurse-card"
            (click)="selectNurseForManagement(nurse)">
            <div class="nurse-header">
              <div class="nurse-avatar">
                <span class="avatar-text">{{ nurse.firstName.charAt(0) }}{{ nurse.lastName.charAt(0) }}</span>
              </div>
              <div class="nurse-info">
                <h3 class="nurse-name">{{ nurse.firstName }} {{ nurse.lastName }}</h3>
                <p class="nurse-email">📧 {{ nurse.email }}</p>
                <p class="nurse-phone" *ngIf="nurse.phone">📞 {{ nurse.phone }}</p>
              </div>
            </div>

            <div class="nurse-stats">
              <div class="stat-item">
                <span class="stat-value">{{ getNurseActiveAppointments(nurse.id) }}</span>
                <span class="stat-label">RDV Actifs</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ getNurseTotalAppointments(nurse.id) }}</span>
                <span class="stat-label">Total RDV</span>
              </div>
            </div>

            <div class="nurse-status">
              <span class="status-badge available">✅ Disponible</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Formulaire d'ajout d'infirmier -->
<div *ngIf="showAddNurseForm" class="add-nurse-overlay">
  <div class="add-nurse-container">
    <!-- Header -->
    <div class="form-header">
      <div class="header-content">
        <div class="header-icon">
          <span class="icon">👨‍⚕️</span>
        </div>
        <div class="header-text">
          <h2 class="form-title">Ajouter un nouvel infirmier</h2>
          <p class="form-subtitle">Veuillez remplir les informations ci-dessous pour créer un compte infirmier.</p>
        </div>
      </div>
      <button mat-icon-button (click)="cancelAddNurse()" class="close-button" aria-label="Fermer le formulaire">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Formulaire -->
    <div class="form-content">
      <form [formGroup]="addNurseForm" (ngSubmit)="onSubmitAddNurse()" class="nurse-form" novalidate>

        <!-- Section Informations personnelles -->
        <div class="form-section personal-info">
          <div class="section-header">
            <div class="section-icon">
              <mat-icon>person</mat-icon>
            </div>
            <h3 class="section-title">Informations personnelles</h3>
          </div>

          <div class="form-grid">
            <mat-form-field  class="form-field">
              <mat-label>Prénom</mat-label>
              <mat-icon matPrefix class="prefix-icon">badge</mat-icon>
              <input matInput formControlName="firstName" placeholder="Entrez le prénom" required>
              <mat-error *ngIf="getFieldError('firstName')">{{ getFieldError('firstName') }}</mat-error>
            </mat-form-field>

            <mat-form-field  class="form-field">
              <mat-label>Nom de famille</mat-label>
              <mat-icon matPrefix class="prefix-icon">family_restroom</mat-icon>
              <input matInput formControlName="lastName" placeholder="Entrez le nom de famille" required>
              <mat-error *ngIf="getFieldError('lastName')">{{ getFieldError('lastName') }}</mat-error>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>Adresse e-mail</mat-label>
              <mat-icon matPrefix class="prefix-icon">email</mat-icon>
              <input matInput type="email" formControlName="email" placeholder="<EMAIL>" required>
              <mat-error *ngIf="getFieldError('email')">{{ getFieldError('email') }}</mat-error>
            </mat-form-field>

            <mat-form-field  class="form-field">
              <mat-label>Numéro de téléphone</mat-label>
              <mat-icon matPrefix class="prefix-icon">phone</mat-icon>
              <input matInput formControlName="phone" placeholder="+216 XX XXX XXX">
              <mat-error *ngIf="getFieldError('phone')">{{ getFieldError('phone') }}</mat-error>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>Nom d'utilisateur</mat-label>
              <mat-icon matPrefix class="prefix-icon">account_circle</mat-icon>
              <input matInput formControlName="username" placeholder="Choisissez un nom d'utilisateur" required>
              <mat-error *ngIf="getFieldError('username')">{{ getFieldError('username') }}</mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Section Sécurité -->
        <div class="form-section security-info">
          <div class="section-header">
            <div class="section-icon">
              <mat-icon>security</mat-icon>
            </div>
            <h3 class="section-title">Informations de sécurité</h3>
          </div>

          <div class="form-grid">
            <mat-form-field class="form-field">
              <mat-label>Mot de passe</mat-label>
              <mat-icon matPrefix class="prefix-icon">lock</mat-icon>
              <input matInput type="password" formControlName="password" placeholder="Minimum 8 caractères" required>
              <mat-hint>Le mot de passe doit contenir au moins 8 caractères.</mat-hint>
              <mat-error *ngIf="getFieldError('password')">{{ getFieldError('password') }}</mat-error>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>Confirmation du mot de passe</mat-label>
              <mat-icon matPrefix class="prefix-icon">lock_outline</mat-icon>
              <input matInput type="password" formControlName="confirmPassword" placeholder="Répétez le mot de passe" required>
              <mat-error *ngIf="getFieldError('confirmPassword')">{{ getFieldError('confirmPassword') }}</mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button mat-stroked-button type="button" (click)="cancelAddNurse()" [disabled]="isSubmittingNurse">
            <mat-icon>cancel</mat-icon> Annuler
          </button>

          <button mat-raised-button color="primary" type="submit" [disabled]="!addNurseForm.valid || isSubmittingNurse">
            <span *ngIf="isSubmittingNurse" class="loading-content">
              <mat-spinner diameter="20"></mat-spinner>
              &nbsp;Ajout en cours…
            </span>
            <span *ngIf="!isSubmittingNurse" class="submit-content">
              <mat-icon>person_add</mat-icon>
              Ajouter l'infirmier
            </span>
          </button>
        </div>

      </form>
    </div>
  </div>
</div>


      <!-- Détails d'un infirmier sélectionné -->
      <div *ngIf="selectedNurseForManagement" class="nurse-details-view">
        <div class="details-header">
          <button mat-icon-button (click)="backToNursesList()" class="back-button">
            <mat-icon>arrow_back</mat-icon>
          </button>
          <h2 class="section-title">
            👩‍⚕️ {{ selectedNurseForManagement.firstName }} {{ selectedNurseForManagement.lastName }}
          </h2>
        </div>

        <div class="nurse-details-content">
          <div class="nurse-info-card">
            <h3>Informations Personnelles</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ selectedNurseForManagement.email }}</span>
              </div>
              <div class="info-item" *ngIf="selectedNurseForManagement.phone">
                <span class="info-label">Téléphone:</span>
                <span class="info-value">{{ selectedNurseForManagement.phone }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Date d'inscription:</span>
                <span class="info-value">{{ formatDate(selectedNurseForManagement.createdAt) }}</span>
              </div>
            </div>
          </div>

          <div class="nurse-appointments-card">
            <h3>Rendez-vous Assignés</h3>
            <p>Détails des rendez-vous à implémenter...</p>
            <!-- TODO: Ajouter la liste des rendez-vous de l'infirmier -->
          </div>
        </div>
      </div>
    </div>

    <!-- Vue Analyses -->
    <div *ngIf="currentView === 'analyses'" class="analyses-section">
      <!-- Liste des analyses -->
      <div *ngIf="!selectedAnalysisForManagement && !showAddAnalysisForm" class="analyses-list-view">
        <div class="section-header">
          <h2 class="section-title">
            🧪 Liste des Analyses
            <span class="count-badge" *ngIf="allAnalyses.length > 0">{{ allAnalyses.length }}</span>
          </h2>
          <button mat-raised-button color="primary" (click)="showAddAnalysisFormToggle()">
            <span class="custom-icon">➕</span>
            Ajouter une analyse
          </button>
        </div>

        <div class="loading-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Chargement des analyses...</p>
        </div>

        <div class="analyses-grid" *ngIf="!isLoading && allAnalyses.length > 0">
          <div
            *ngFor="let analysis of allAnalyses"
            class="analysis-card"
            [class.inactive]="!analysis.isActive"
            (click)="selectAnalysisForManagement(analysis)"
          >
            <div class="analysis-header">
              <div class="analysis-info">
                <h3 class="analysis-name">{{ analysis.name }}</h3>
                <span class="analysis-category">{{ analysis.category }}</span>
              </div>
              <div class="analysis-status">
                <span class="status-badge" [class.active]="analysis.isActive" [class.inactive]="!analysis.isActive">
                  {{ analysis.isActive ? 'Actif' : 'Inactif' }}
                </span>
              </div>
            </div>

            <div class="analysis-details">
              <p class="analysis-description">{{ analysis.description }}</p>

              <div class="analysis-meta">
                <div class="meta-item">
                  <span class="meta-label">💰 Prix:</span>
                  <span class="meta-value">{{ analysis.price }}€</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">⏱️ Durée:</span>
                  <span class="meta-value">{{ analysis.duration }} min</span>
                </div>
              </div>
            </div>

            <div class="analysis-actions">
              <button mat-icon-button color="primary" title="Modifier">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button [color]="analysis.isActive ? 'warn' : 'accent'"
                      [title]="analysis.isActive ? 'Désactiver' : 'Activer'">
                <mat-icon>{{ analysis.isActive ? 'visibility_off' : 'visibility' }}</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <div class="empty-state" *ngIf="!isLoading && allAnalyses.length === 0">
          <div class="empty-icon">🧪</div>
          <h3>Aucune analyse trouvée</h3>
          <p>Commencez par ajouter votre première analyse.</p>
          <button mat-raised-button color="primary" (click)="showAddAnalysisFormToggle()">
            <span class="custom-icon">➕</span>
            Ajouter une analyse
          </button>
        </div>
      </div>

      <!-- Formulaire d'ajout d'analyse -->
      <div *ngIf="showAddAnalysisForm" class="add-analysis-form">
        <div class="form-header">
          <h2 class="form-title">➕ Ajouter une nouvelle analyse</h2>
          <button mat-icon-button (click)="cancelAddAnalysis()" class="close-btn">
            <mat-icon>close</mat-icon>
          </button>
        </div>

        <form [formGroup]="addAnalysisForm" (ngSubmit)="onSubmitAddAnalysis()" class="analysis-form">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Nom de l'analyse</mat-label>
              <input matInput formControlName="name" placeholder="Ex: Analyse sanguine complète">
              <mat-error>{{ getAnalysisFieldError('name') }}</mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Catégorie</mat-label>
              <mat-select formControlName="category">
                <mat-option value="Hématologie">Hématologie</mat-option>
                <mat-option value="Biochimie">Biochimie</mat-option>
                <mat-option value="Microbiologie">Microbiologie</mat-option>
                <mat-option value="Immunologie">Immunologie</mat-option>
                <mat-option value="Endocrinologie">Endocrinologie</mat-option>
                <mat-option value="Cardiologie">Cardiologie</mat-option>
                <mat-option value="Autre">Autre</mat-option>
              </mat-select>
              <mat-error>{{ getAnalysisFieldError('category') }}</mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="3"
                        placeholder="Description détaillée de l'analyse..."></textarea>
              <mat-error>{{ getAnalysisFieldError('description') }}</mat-error>
            </mat-form-field>
          </div>

          <div class="form-row two-columns">
            <mat-form-field appearance="outline">
              <mat-label>Prix (€)</mat-label>
              <input matInput type="number" formControlName="price" min="0" step="0.01">
              <mat-error>{{ getAnalysisFieldError('price') }}</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Durée (minutes)</mat-label>
              <input matInput type="number" formControlName="duration" min="1">
              <mat-error>{{ getAnalysisFieldError('duration') }}</mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-checkbox formControlName="isActive">
              Analyse active (disponible pour les rendez-vous)
            </mat-checkbox>
          </div>

          <div class="form-actions">
            <button mat-stroked-button type="button" (click)="cancelAddAnalysis()" [disabled]="isSubmittingAnalysis">
              Annuler
            </button>
            <button mat-raised-button color="primary" type="submit" [disabled]="!addAnalysisForm.valid || isSubmittingAnalysis">
              <mat-spinner diameter="20" *ngIf="isSubmittingAnalysis"></mat-spinner>
              <span *ngIf="!isSubmittingAnalysis">Ajouter l'analyse</span>
              <span *ngIf="isSubmittingAnalysis">Ajout en cours...</span>
            </button>
          </div>
        </form>
      </div>

      <!-- Détails d'une analyse sélectionnée -->
      <div *ngIf="selectedAnalysisForManagement" class="analysis-details-view">
        <div class="details-header">
          <button mat-icon-button (click)="backToAnalysesList()" class="back-btn">
            <mat-icon>arrow_back</mat-icon>
          </button>
          <h2 class="details-title">🧪 {{ selectedAnalysisForManagement.name }}</h2>
        </div>

        <div class="analysis-info-card">
          <div class="info-section">
            <h3>Informations générales</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Nom:</span>
                <span class="info-value">{{ selectedAnalysisForManagement.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Catégorie:</span>
                <span class="info-value">{{ selectedAnalysisForManagement.category }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Prix:</span>
                <span class="info-value">{{ selectedAnalysisForManagement.price }}€</span>
              </div>
              <div class="info-item">
                <span class="info-label">Durée:</span>
                <span class="info-value">{{ selectedAnalysisForManagement.duration }} minutes</span>
              </div>
              <div class="info-item">
                <span class="info-label">Statut:</span>
                <span class="info-value">
                  <span class="status-badge" [class.active]="selectedAnalysisForManagement.isActive"
                        [class.inactive]="!selectedAnalysisForManagement.isActive">
                    {{ selectedAnalysisForManagement.isActive ? 'Actif' : 'Inactif' }}
                  </span>
                </span>
              </div>
            </div>
          </div>

          <div class="info-section">
            <h3>Description</h3>
            <p class="description-text">{{ selectedAnalysisForManagement.description }}</p>
          </div>

          <div class="actions-section">
            <button mat-raised-button color="primary">
              <mat-icon>edit</mat-icon>
              Modifier
            </button>
            <button mat-stroked-button [color]="selectedAnalysisForManagement.isActive ? 'warn' : 'accent'">
              <mat-icon>{{ selectedAnalysisForManagement.isActive ? 'visibility_off' : 'visibility' }}</mat-icon>
              {{ selectedAnalysisForManagement.isActive ? 'Désactiver' : 'Activer' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Vue Statistiques -->
    <div *ngIf="currentView === 'statistics'" class="statistics-section">
      <h2>📊 Tableau de bord statistiques</h2>

      <!-- Statistiques principales -->
      <div class="stats-overview">
        <div class="stats-row">
          <div class="stat-card-large">
            <div class="stat-header">
              <h3>📋 Rendez-vous</h3>
              <div class="stat-total">{{ statistics.appointments.total }}</div>
            </div>
            <div class="stat-breakdown">
              <div class="stat-item">
                <span class="stat-label">En attente</span>
                <span class="stat-value pending">{{ statistics.appointments.pending }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Confirmés</span>
                <span class="stat-value confirmed">{{ statistics.appointments.confirmed }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">En cours</span>
                <span class="stat-value in-progress">{{ statistics.appointments.inProgress }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Terminés</span>
                <span class="stat-value completed">{{ statistics.appointments.completed }}</span>
              </div>
            </div>
          </div>

          <div class="stat-card-large">
            <div class="stat-header">
              <h3>👥 Patients</h3>
              <div class="stat-total">{{ statistics.patients.total }}</div>
            </div>
            <div class="stat-breakdown">
              <div class="stat-item">
                <span class="stat-label">Actifs</span>
                <span class="stat-value active">{{ statistics.patients.active }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Taux d'activité</span>
                <span class="stat-value">{{ statistics.patients.total > 0 ? ((statistics.patients.active / statistics.patients.total) * 100).toFixed(1) : 0 }}%</span>
              </div>
            </div>
          </div>

          <div class="stat-card-large">
            <div class="stat-header">
              <h3>👩‍⚕️ Infirmiers</h3>
              <div class="stat-total">{{ statistics.nurses.total }}</div>
            </div>
            <div class="stat-breakdown">
              <div class="stat-item">
                <span class="stat-label">Disponibles</span>
                <span class="stat-value available">{{ statistics.nurses.available }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Occupés</span>
                <span class="stat-value busy">{{ statistics.nurses.busy }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Graphiques et métriques -->
      <div class="charts-section">
        <div class="chart-row">
          <!-- Graphique en secteurs des statuts -->
          <div class="chart-card">
            <h3>📊 Répartition des rendez-vous</h3>
            <div class="pie-chart">
              <div class="pie-segment pending"
                   [style.--percentage]="statistics.appointments.total > 0 ? (statistics.appointments.pending / statistics.appointments.total * 100) : 0">
                <span class="pie-label">En attente<br>{{ statistics.appointments.pending }}</span>
              </div>
              <div class="pie-segment confirmed"
                   [style.--percentage]="statistics.appointments.total > 0 ? (statistics.appointments.confirmed / statistics.appointments.total * 100) : 0">
                <span class="pie-label">Confirmés<br>{{ statistics.appointments.confirmed }}</span>
              </div>
              <div class="pie-segment completed"
                   [style.--percentage]="statistics.appointments.total > 0 ? (statistics.appointments.completed / statistics.appointments.total * 100) : 0">
                <span class="pie-label">Terminés<br>{{ statistics.appointments.completed }}</span>
              </div>
            </div>
          </div>

          <!-- Métriques de performance -->
          <div class="metrics-card">
            <h3>📈 Indicateurs de performance</h3>
            <div class="metrics-list">
              <div class="metric-item">
                <div class="metric-label">Taux de completion</div>
                <div class="metric-value">
                  <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="statistics.performance.completionRate"></div>
                  </div>
                  <span class="metric-percentage">{{ statistics.performance.completionRate }}%</span>
                </div>
              </div>

              <div class="metric-item">
                <div class="metric-label">Efficacité des infirmiers</div>
                <div class="metric-value">
                  <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="statistics.nurses.total > 0 ? (statistics.nurses.busy / statistics.nurses.total * 100) : 0"></div>
                  </div>
                  <span class="metric-percentage">{{ statistics.nurses.total > 0 ? ((statistics.nurses.busy / statistics.nurses.total) * 100).toFixed(1) : 0 }}%</span>
                </div>
              </div>

              <div class="metric-item">
                <div class="metric-label">Patients actifs</div>
                <div class="metric-value">
                  <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="statistics.patients.total > 0 ? (statistics.patients.active / statistics.patients.total * 100) : 0"></div>
                  </div>
                  <span class="metric-percentage">{{ statistics.patients.total > 0 ? ((statistics.patients.active / statistics.patients.total) * 100).toFixed(1) : 0 }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tableau récapitulatif -->
      <div class="summary-table">
        <h3>📋 Résumé détaillé</h3>
        <div class="table-container">
          <table class="stats-table">
            <thead>
              <tr>
                <th>Catégorie</th>
                <th>Total</th>
                <th>Actifs/Disponibles</th>
                <th>En cours/Occupés</th>
                <th>Taux d'utilisation</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>📋 Rendez-vous</td>
                <td>{{ statistics.appointments.total }}</td>
                <td>{{ statistics.appointments.confirmed + statistics.appointments.inProgress }}</td>
                <td>{{ statistics.appointments.inProgress }}</td>
                <td>{{ statistics.appointments.total > 0 ? (((statistics.appointments.confirmed + statistics.appointments.inProgress) / statistics.appointments.total) * 100).toFixed(1) : 0 }}%</td>
              </tr>
              <tr>
                <td>👥 Patients</td>
                <td>{{ statistics.patients.total }}</td>
                <td>{{ statistics.patients.active }}</td>
                <td>-</td>
                <td>{{ statistics.patients.total > 0 ? ((statistics.patients.active / statistics.patients.total) * 100).toFixed(1) : 0 }}%</td>
              </tr>
              <tr>
                <td>👩‍⚕️ Infirmiers</td>
                <td>{{ statistics.nurses.total }}</td>
                <td>{{ statistics.nurses.available }}</td>
                <td>{{ statistics.nurses.busy }}</td>
                <td>{{ statistics.nurses.total > 0 ? ((statistics.nurses.busy / statistics.nurses.total) * 100).toFixed(1) : 0 }}%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Vue Suivi Temps Réel -->
    <div *ngIf="currentView === 'tracking'" class="tracking-section">
      <app-admin-tracking-map></app-admin-tracking-map>
    </div>

    <!-- Vue Rendez-vous -->
    <div *ngIf="currentView === 'appointments'" class="appointments-view">
      <!-- Actions rapides -->
      <div class="actions-section">
        <div class="actions-grid">
          <button mat-raised-button color="accent" (click)="refreshData()" [disabled]="isLoading">
            <span class="custom-icon">🔄</span>
            Actualiser
          </button>

          <button mat-stroked-button (click)="showAllAppointments = !showAllAppointments">
            <span class="custom-icon">{{ showAllAppointments ? '👁️‍🗨️' : '👁️' }}</span>
            {{ showAllAppointments ? 'Masquer tous' : 'Voir tous les RDV' }}
          </button>

          <button mat-stroked-button (click)="showConfirmedAppointments = !showConfirmedAppointments">
            <span class="custom-icon">{{ showConfirmedAppointments ? '🙈' : '✅' }}</span>
            {{ showConfirmedAppointments ? 'Masquer confirmés' : 'Voir RDV confirmés' }}
          </button>
        </div>
      </div>

    <!-- Rendez-vous en attente -->
    <div class="appointments-section">
      <h2 class="section-title">
        ⏳ Rendez-vous en attente d'affectation
        <span class="count-badge" *ngIf="pendingAppointments.length > 0">{{ pendingAppointments.length }}</span>
      </h2>

      <div class="appointments-list" *ngIf="pendingAppointments.length > 0">
        <div
          *ngFor="let appointment of pendingAppointments"
          class="appointment-card admin-card"
          [class.urgent]="appointment.isUrgent"
        >
          <!-- Card Header -->
          <div class="card-header">
            <div class="appointment-info">
              <h3 class="appointment-title">
                👤 {{ appointment.patient.firstName }} {{ appointment.patient.lastName }}
                <span *ngIf="appointment.isUrgent" class="urgent-badge">🚨 URGENT</span>
              </h3>
              <div class="appointment-date">
                📅 {{ appointment.scheduledDate | date:'dd/MM/yyyy HH:mm' }}
              </div>
              <div class="patient-contact">
                📧 {{ appointment.patient.email }}
              </div>
            </div>
            <div class="status-badge pending">
              ⏳ En attente
            </div>
          </div>

          <!-- Card Body -->
          <div class="card-body">
            <div class="appointment-details">
              <div class="detail-item">
                <span class="detail-label">📍 Adresse :</span>
                <span class="detail-value">{{ appointment.homeAddress }}</span>
              </div>

              <div class="detail-item" *ngIf="appointment.analysisTypes && appointment.analysisTypes.length > 0">
                <span class="detail-label">🔬 Analyses :</span>
                <div class="analysis-chips">
                  <mat-chip-set>
                    <mat-chip *ngFor="let analysis of appointment.analysisTypes">
                      {{ analysis.name }}
                    </mat-chip>
                  </mat-chip-set>
                </div>
              </div>

              <div class="detail-item" *ngIf="appointment.symptoms">
                <span class="detail-label">🩺 Symptômes :</span>
                <span class="detail-value">{{ appointment.symptoms }}</span>
              </div>
            </div>
          </div>

          <!-- Card Actions -->
          <div class="card-actions">
            <div class="primary-actions">
              <button
                mat-raised-button
                color="primary"
                (click)="autoAssignNurse(appointment)"
                [disabled]="isLoading || !canAssignNurses()"
                [title]="!canAssignNurses() ? 'Permission requise: ASSIGN_NURSES' : ''"
                class="action-btn"
              >
                <span class="custom-icon">🤖</span>
                Affecter automatiquement
              </button>

              <button
                mat-raised-button
                color="accent"
                (click)="openNurseSelectionModal(appointment)"
                [disabled]="isLoading || !canAssignNurses()"
                [title]="!canAssignNurses() ? 'Permission requise: ASSIGN_NURSES' : ''"
                class="action-btn"
              >
                <span class="custom-icon">👩‍⚕️</span>
                Choisir un infirmier
              </button>
            </div>

            <!-- Indicateur de permissions -->
            <div *ngIf="!canAssignNurses()" class="permission-warning">
              <mat-icon>lock</mat-icon>
              <span>Permissions insuffisantes</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="pendingAppointments.length === 0 && !isLoading">
        <div class="empty-icon">✅</div>
        <h3>Aucun rendez-vous en attente</h3>
        <p>Tous les rendez-vous ont été affectés à des infirmiers.</p>
      </div>
    </div>

    <!-- Rendez-vous confirmés -->
    <div class="appointments-section" *ngIf="showConfirmedAppointments">
      <h2 class="section-title">
        ✅ Rendez-vous confirmés et en cours
        <span class="count-badge" *ngIf="confirmedAppointments.length > 0">{{ confirmedAppointments.length }}</span>
      </h2>

      <div class="appointments-list" *ngIf="confirmedAppointments.length > 0">
        <div
          *ngFor="let appointment of confirmedAppointments"
          class="appointment-card admin-card confirmed-card"
          [class.urgent]="appointment.isUrgent"
          [class.in-progress]="appointment.status === 'IN_PROGRESS'"
        >
          <!-- Card Header -->
          <div class="card-header">
            <div class="appointment-info">
              <h3 class="appointment-title">
                👤 {{ appointment.patient.firstName }} {{ appointment.patient.lastName }}
                <span *ngIf="appointment.isUrgent" class="urgent-badge">🚨 URGENT</span>
              </h3>
              <div class="appointment-date">
                📅 {{ appointment.scheduledDate | date:'dd/MM/yyyy HH:mm' }}
              </div>
              <div class="patient-contact">
                📧 {{ appointment.patient.email }}
              </div>
              <div class="nurse-info" *ngIf="appointment.nurse">
                👩‍⚕️ Infirmier: {{ appointment.nurse.firstName }} {{ appointment.nurse.lastName }}
              </div>
            </div>
            <div class="status-badge" [ngClass]="getStatusClass(appointment.status)">
              {{ getStatusLabel(appointment.status) }}
            </div>
          </div>

          <!-- Card Body -->
          <div class="card-body">
            <div class="appointment-details">
              <div class="detail-item">
                <span class="detail-label">📍 Adresse :</span>
                <span class="detail-value">{{ appointment.homeAddress }}</span>
              </div>

              <div class="detail-item" *ngIf="appointment.analysisTypes && appointment.analysisTypes.length > 0">
                <span class="detail-label">🔬 Analyses :</span>
                <div class="analysis-chips">
                  <mat-chip-set>
                    <mat-chip *ngFor="let analysis of appointment.analysisTypes">
                      {{ analysis.name }}
                    </mat-chip>
                  </mat-chip-set>
                </div>
              </div>

              <div class="detail-item" *ngIf="appointment.symptoms">
                <span class="detail-label">🩺 Symptômes :</span>
                <span class="detail-value">{{ appointment.symptoms }}</span>
              </div>

              <div class="detail-item" *ngIf="appointment.specialInstructions">
                <span class="detail-label">📋 Instructions :</span>
                <span class="detail-value">{{ appointment.specialInstructions }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">💰 Prix total :</span>
                <span class="detail-value">{{ appointment.totalPrice }} DT</span>
              </div>
            </div>
          </div>

          <!-- Progress indicator for in-progress appointments -->
          <div class="progress-section" *ngIf="appointment.status === 'IN_PROGRESS'">
            <div class="progress-info">
              <span class="progress-icon">🔄</span>
              <span class="progress-text">Mission en cours...</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State for confirmed appointments -->
      <div class="empty-state" *ngIf="confirmedAppointments.length === 0 && !isLoading">
        <div class="empty-icon">📋</div>
        <h3>Aucun rendez-vous confirmé</h3>
        <p>Aucun rendez-vous n'a encore été confirmé ou assigné à un infirmier.</p>
      </div> <!-- Fin de la vue appointments -->

    <!-- Spinner de chargement -->
    <div *ngIf="isLoading" class="loading-overlay">
      <div class="spinner">🔄</div>
      <p>Chargement en cours...</p>
    </div>
  </div>
</div>

<!-- Modal de sélection d'infirmier -->
<div *ngIf="showNurseModal" class="modal-overlay" (click)="closeNurseModal()">
  <div class="nurse-modal" (click)="$event.stopPropagation()">
    <!-- Header de la modal -->
    <div class="modal-header">
      <div class="modal-title-section">
        <h2 class="modal-title">
          <span class="modal-icon">👩‍⚕️</span>
          Assigner un infirmier
        </h2>
        <p class="modal-subtitle">
          Choisissez le meilleur infirmier pour le rendez-vous #{{ selectedAppointmentForNurse?.id }}
        </p>
      </div>
      <button class="close-btn" (click)="closeNurseModal()">
        <span class="close-icon">✕</span>
      </button>
    </div>

    <!-- Informations du rendez-vous -->
    <div class="appointment-summary" *ngIf="selectedAppointmentForNurse">
      <div class="summary-card">
        <div class="summary-header">
          <span class="summary-icon">🎯</span>
          <span class="summary-title">Détails du rendez-vous</span>
        </div>
        <div class="summary-content">
          <div class="summary-row">
            <span class="summary-label">👤 Patient :</span>
            <span class="summary-value">{{ selectedAppointmentForNurse.patient.firstName }} {{ selectedAppointmentForNurse.patient.lastName }}</span>
          </div>
          <div class="summary-row">
            <span class="summary-label">📅 Date :</span>
            <span class="summary-value">{{ formatDateTime(selectedAppointmentForNurse.scheduledDate) }}</span>
          </div>
          <div class="summary-row">
            <span class="summary-label">🏠 Adresse :</span>
            <span class="summary-value">{{ selectedAppointmentForNurse.homeAddress }}</span>
          </div>
          <div class="summary-row" *ngIf="selectedAppointmentForNurse.isUrgent">
            <span class="summary-label">⚡ Priorité :</span>
            <span class="summary-value urgent-text">🚨 URGENT</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Liste des infirmiers disponibles -->
    <div class="nurses-section">
      <div class="section-header">
        <h3 class="section-title">
          <span class="section-icon">🌟</span>
          Infirmiers disponibles ({{ availableNurses.length }})
        </h3>
        <div class="nurses-filter">
          <input
            type="text"
            placeholder="🔍 Rechercher par nom ou email..."
            class="search-input"
            [(ngModel)]="nurseSearchTerm"
            (input)="filterNurses()">
          <span class="search-icon">✨</span>
        </div>
      </div>

      <div class="nurses-grid" *ngIf="filteredNurses.length > 0">
        <div
          *ngFor="let nurse of filteredNurses"
          class="nurse-card"
          [class.selected]="selectedNurse?.id === nurse.id"
          (click)="selectNurse(nurse)">

          <div class="nurse-avatar">
            <div class="avatar-circle">
              {{ nurse.firstName.charAt(0) }}{{ nurse.lastName.charAt(0) }}
            </div>
            <div class="availability-indicator" [class.available]="isNurseAvailable(nurse)">
              <span class="indicator-dot"></span>
            </div>
          </div>

          <div class="nurse-info">
            <h4 class="nurse-name">{{ nurse.firstName }} {{ nurse.lastName }}</h4>
            <p class="nurse-email">{{ nurse.email }}</p>
            <div class="nurse-stats">
              <div class="stat-item">
                <span class="stat-icon">📊</span>
                <span class="stat-text">{{ getNurseActiveAppointments(nurse.id!) }} RDV actifs</span>
              </div>
              <div class="stat-item">
                <span class="stat-icon">🏆</span>
                <span class="stat-text">{{ getNurseRating(nurse.id!) }}/5</span>
              </div>
            </div>
          </div>

          <div class="nurse-actions">
            <div class="selection-indicator" *ngIf="selectedNurse?.id === nurse.id">
              <span class="check-icon">✓</span>
            </div>
            <div class="availability-status" [class.available]="isNurseAvailable(nurse)">
              {{ isNurseAvailable(nurse) ? '✅ Disponible' : '⏳ Occupé' }}
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="filteredNurses.length === 0" class="empty-nurses">
        <div class="empty-icon">🔍</div>
        <h3>Aucun infirmier trouvé</h3>
        <p *ngIf="nurseSearchTerm">✨ Aucun infirmier ne correspond à votre recherche "{{ nurseSearchTerm }}"</p>
        <p *ngIf="!nurseSearchTerm">💫 Aucun infirmier n'est disponible pour le moment.</p>
      </div>
    </div>

    <!-- Actions de la modal -->
    <div class="modal-actions">
      <button
        class="action-btn secondary"
        (click)="closeNurseModal()">
        ❌ Annuler
      </button>
      <button
        class="action-btn primary"
        [disabled]="!selectedNurse || isAssigning"
        (click)="confirmNurseAssignment()">
        <div class="button-content">
          <span class="modal-icon">✨</span>
          <span *ngIf="!isAssigning">🚀 Confirmer l'assignation</span>
          <span *ngIf="isAssigning">
            <span class="loading-spinner">✨</span>
            🔄 Assignation en cours...
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
