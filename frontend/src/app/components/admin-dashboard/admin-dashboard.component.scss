// ========== STRUCTURE EXACTEMENT IDENTIQUE AUX AUTRES DASHBOARDS ==========

:host {
  display: block;
}

.admin-dashboard-container {
  min-height: calc(100vh - 80px);
  background-color: #f9fafb;
  padding: 1.5rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.welcome-section {
  flex: 1;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dashboard-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
}

.quick-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

// Navigation entre vues
.view-toggle {
  display: flex;
  gap: 8px;
  margin-right: 16px;

  button {
    border-radius: 20px;

    &.active {
      background-color: #1976d2;
      color: white;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.stat-icon {
  font-size: 2.5rem;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
}

.actions-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.actions-grid {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.appointments-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.count-badge {
  background: #ef4444;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.appointment-card {
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1976d2;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.urgent {
    border-color: #ef4444;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.appointment-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.urgent-badge {
  font-size: 0.75rem;
  background: #ef4444;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  animation: pulse 2s infinite;
}

.appointment-date, .patient-contact {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 4px;
}

.status-badge {
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;

  &.pending {
    background: #fef3c7;
    color: #92400e;
  }
}

.card-body {
  padding: 24px;
}

.appointment-details {
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-label {
  font-weight: 500;
  color: #64748b;
  min-width: 120px;
  flex-shrink: 0;
}

.detail-value {
  color: #1e293b;
  flex: 1;
}

.analysis-chips {
  flex: 1;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.primary-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  min-width: 180px;
}

.empty-state {
  text-align: center;
  padding: 48px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Styles pour les icônes personnalisées */
.custom-icon {
  font-size: 1.2rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  min-width: 20px;
}

.dashboard-title .custom-icon {
  font-size: 1.5rem;
  margin-right: 12px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
  }

  .card-actions {
    flex-direction: column;
    gap: 12px;
  }

  .primary-actions {
    width: 100%;
    justify-content: center;
  }

  .action-btn {
    min-width: auto;
    flex: 1;
  }
}

/* Styles pour les rendez-vous confirmés */
.confirmed-card {
  border-left: 4px solid #4caf50 !important;
  background: linear-gradient(135deg, #ffffff 0%, #f1f8e9 100%);
}

.confirmed-card.in-progress {
  border-left-color: #2196f3 !important;
  background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
}

.nurse-info {
  font-size: 0.875rem;
  color: #4caf50;
  font-weight: 500;
  margin-top: 4px;
}

.progress-section {
  padding: 12px 24px;
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border-top: 1px solid #e0e0e0;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-icon {
  animation: spin 2s linear infinite;
}

.progress-text {
  font-weight: 500;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Styles pour les badges de statut */
.status-badge.confirmed {
  background-color: #4caf50;
  color: white;
}

.status-badge.assigned {
  background-color: #ff9800;
  color: white;
}

.status-badge.on-way {
  background-color: #2196f3;
  color: white;
}

.status-badge.in-progress {
  background-color: #f57c00;
  color: white;
  animation: pulse 2s infinite;
}

.status-badge.sampling-done {
  background-color: #607d8b;
  color: white;
}

.status-badge.completed {
  background-color: #4caf50;
  color: white;
}

.status-badge.cancelled {
  background-color: #f44336;
  color: white;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

// ========== STYLES POUR LA SECTION PATIENTS ==========

.patients-section {
  margin-top: 24px;
}

.patients-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 20px;
}

// Styles pour la structure horizontale des cartes patients
.patient-card-content {
  display: flex;
  align-items: center;
  gap: 2rem;
  width: 100%;
}

.patient-main-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 250px;
}

.patient-contact-info {
  flex: 1;
  min-width: 200px;
}

.patient-stats-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  min-width: 150px;
}

.patient-avatar {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.patient-info {
  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #666;
    font-size: 14px;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.patient-stats {
  display: flex;
  gap: 16px;
  margin-top: 12px;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 8px;
    min-width: 60px;

    &.pending {
      background: #fff3e0;
      color: #f57c00;
    }

    .stat-number {
      font-size: 18px;
      font-weight: bold;
      line-height: 1;
    }

    .stat-label {
      font-size: 11px;
      text-transform: uppercase;
      margin-top: 2px;
    }
  }
}

// Détails du patient sélectionné
.patient-details-view {
  margin-top: 24px;
}

.patient-header {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;

  .back-button {
    background: #f5f5f5;

    &:hover {
      background: #e0e0e0;
    }
  }

  .patient-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
  }
}

.patient-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  font-weight: bold;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.patient-details {
  h2 {
    margin: 0 0 4px 0;
    color: #333;
    font-size: 24px;
  }

  .patient-email {
    color: #666;
    margin: 0 0 8px 0;
    font-size: 16px;
  }

  .patient-contact {
    display: flex;
    gap: 16px;

    span {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #666;
      font-size: 14px;

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

.appointments-title {
  color: #333;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.count-badge {
  background: #1976d2;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

// Styles pour les cartes de rendez-vous dans la vue patient
.appointments-list {
  .appointment-card {
    margin-bottom: 16px;
    border-left: 4px solid #1976d2;

    .appointment-date {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
    }

    .appointment-details {
      .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        color: #666;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          color: #999;
        }
      }
    }
  }
}

// Classes de statut pour les chips
.pending {
  background-color: #fff3e0 !important;
  color: #f57c00 !important;
}

.confirmed {
  background-color: #e8f5e8 !important;
  color: #2e7d32 !important;
}

.assigned {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
}

.on-way {
  background-color: #fff7ed !important;
  color: #f57c00 !important;
}

.in-progress {
  background-color: #fff8e1 !important;
  color: #f57c00 !important;
}

.sampling-done {
  background-color: #e0f2f1 !important;
  color: #00695c !important;
}

.completed {
  background-color: #e8f5e8 !important;
  color: #2e7d32 !important;
}

.cancelled {
  background-color: #ffebee !important;
  color: #c62828 !important;
}

// ========== NOUVEAUX STYLES MODERNES ==========

// Navigation entre vues
.view-navigation {
  display: flex;
  gap: 0.5rem;
  background: #f3f4f6;
  padding: 0.25rem;
  border-radius: 0.5rem;
}

.nav-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: 0.375rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }

  &.active {
    background: white;
    color: #2563eb;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }
}

// Actions rapides modernes
.action-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 0.5rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.15s ease;

  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  &.primary {
    background: #2563eb;
    color: white;
    border-color: #2563eb;

    &:hover {
      background: #1d4ed8;
    }
  }
}

// Cartes patients modernes
.patient-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  cursor: pointer;
  transition: all 0.15s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #2563eb;
  }
}

.patient-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.patient-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
  font-weight: bold;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.patient-basic-info {
  flex: 1;
}

.patient-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.patient-email {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.patient-details {
  margin-bottom: 1rem;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;

  .info-icon {
    font-size: 1rem;
  }

  .info-text {
    color: #6b7280;
    font-size: 0.875rem;
  }
}

.patient-stats {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    background: #f9fafb;
    border-radius: 0.375rem;
    min-width: 3rem;

    &.urgent {
      background: #fef3c7;
      color: #92400e;
    }

    .stat-number {
      font-size: 1rem;
      font-weight: bold;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.625rem;
      text-transform: uppercase;
      margin-top: 0.125rem;
    }
  }
}

.patient-actions {
  .view-btn {
    padding: 0.5rem 1rem;
    background: #2563eb;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    white-space: nowrap;

    &:hover {
      background: #1d4ed8;
      transform: translateY(-1px);
    }
  }
}

// Spinner moderne
.loading-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;

  .spinner {
    font-size: 2rem;
    animation: spin 1s linear infinite;
  }

  p {
    margin-top: 1rem;
    color: #6b7280;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// État vide moderne
.empty-state {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  h3 {
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  p {
    color: #9ca3af;
  }
}

// ========== STYLES POUR LA SECTION INFIRMIERS ==========

.nurses-section {
  margin-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.nurses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.nurse-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #1976d2;
  }
}

.nurse-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.nurse-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.nurse-info {
  flex: 1;

  .nurse-name {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }

  .nurse-email, .nurse-phone {
    margin: 2px 0;
    font-size: 14px;
    color: #6b7280;
  }
}

.nurse-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-item {
  text-align: center;

  .stat-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #1976d2;
  }

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
  }
}

.nurse-status {
  display: flex;
  justify-content: center;

  .status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;

    &.available {
      background: #dcfce7;
      color: #166534;
    }

    &.busy {
      background: #fef3c7;
      color: #92400e;
    }

    &.offline {
      background: #fee2e2;
      color: #991b1b;
    }
  }
}

// Nouveau design pour le formulaire d'ajout d'infirmier
.add-nurse-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.add-nurse-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.4s ease-out;
}

.form-header {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  .icon {
    font-size: 3rem;
    display: block;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
}

.header-text {
  .form-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
  }

  .form-subtitle {
    font-size: 0.975rem;
    opacity: 0.9;
    margin: 0;
    line-height: 1.4;
  }
}

.close-button {
  color: white !important;
  opacity: 0.8;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1) !important;
    transform: scale(1.1);
  }
}

.form-content {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.nurse-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }
}

.personal-info {
  border-left: 4px solid #10b981;
}

.security-info {
  border-left: 4px solid #f59e0b;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.section-icon {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  border-radius: 12px;
  padding: 0.75rem;

  mat-icon {
    color: #0277bd;
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  align-items: start;
}

.security-grid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

// Amélioration de l'espacement des champs
.form-field {
  width: 100%;
  margin-bottom: 1rem;

  ::ng-deep {
    .mat-mdc-form-field-wrapper {
      padding-bottom: 0;
    }

    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 0.25rem;
      min-height: 20px;
    }

    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-radius: 12px !important;
    }

    // Styles pour les icônes prefix
    .mat-mdc-form-field-icon-prefix {
      color: #6b7280;
      margin-right: 8px;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }

    // Styles pour les erreurs
    .mat-mdc-form-field-error {
      color: #dc2626;
      font-size: 12px;
      margin-top: 4px;
    }

    // Styles pour les labels
    .mat-mdc-form-field-label {
      color: #374151;
    }

    // Styles pour les inputs
    .mat-mdc-input-element {
      color: #111827;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 2px solid #f1f5f9;
  margin-top: 1rem;
}

.cancel-button {
  padding: 0 2rem;
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover:not([disabled]) {
    background: #fee2e2;
    border-color: #f87171;
    color: #dc2626;
    transform: translateY(-1px);
  }

  mat-icon {
    margin-right: 0.5rem;
  }
}

.submit-button {
  padding: 0 2rem;
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  min-width: 200px;
  transition: all 0.2s ease;

  &:hover:not([disabled]) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(37, 99, 235, 0.3);
  }

  &:disabled {
    opacity: 0.6;
  }
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 1rem;

  mat-spinner {
    ::ng-deep circle {
      stroke: white;
    }
  }
}

.submit-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  mat-icon {
    font-size: 1.25rem;
    width: 1.25rem;
    height: 1.25rem;
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .add-nurse-container {
    width: 98%;
    margin: 1rem;
    border-radius: 16px;
  }

  .form-header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;

    .header-content {
      flex-direction: column;
      gap: 1rem;
    }

    .close-button {
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
  }

  .form-content {
    padding: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 1rem;

    button {
      width: 100%;
    }
  }

  .header-text {
    .form-title {
      font-size: 1.5rem;
    }

    .form-subtitle {
      font-size: 0.875rem;
    }
  }
}

@media (max-width: 480px) {
  .add-nurse-overlay {
    padding: 0.5rem;
  }

  .section-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .form-grid {
    gap: 1rem;
  }

  .form-section {
    padding: 1rem;
  }
}

// Styles spécifiques pour les icônes prefix
.prefix-icon {
  color: #6b7280 !important;
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  margin-right: 8px !important;
}

// Forcer l'affichage correct des icônes Material
::ng-deep {
  .mat-mdc-form-field-icon-prefix {
    .mat-icon {
      color: #6b7280;
      font-size: 20px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }

  // Forcer l'affichage correct des erreurs
  .mat-mdc-form-field-error {
    color: #dc2626 !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
    display: block !important;
    margin-top: 4px !important;
  }

  // S'assurer que les labels s'affichent correctement
  .mat-mdc-form-field-label {
    color: #374151 !important;
  }

  // S'assurer que les hints s'affichent correctement
  .mat-mdc-form-field-hint {
    color: #6b7280 !important;
    font-size: 12px !important;
  }
}

// Améliorer l'accessibilité
.form-field {
  &:focus-within {
    ::ng-deep .mdc-notched-outline {
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
  }
}

// Animation pour les erreurs
::ng-deep .mat-mdc-form-field-error {
  animation: shake 0.3s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

// Animation pour le spinner dans le bouton
.mat-mdc-progress-spinner {
  vertical-align: middle;
}

// Détails d'un infirmier
.nurse-details-view {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.details-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;

  .back-button {
    color: #6b7280;

    &:hover {
      color: #1976d2;
    }
  }
}

.nurse-details-content {
  display: grid;
  gap: 24px;
}

.nurse-info-card, .nurse-appointments-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;

  h3 {
    margin: 0 0 16px 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
  }
}

.info-grid {
  display: grid;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;

  &:last-child {
    border-bottom: none;
  }

  .info-label {
    font-weight: 500;
    color: #6b7280;
  }

  .info-value {
    color: #1f2937;
  }
}

// ========== STYLES POUR LES RENDEZ-VOUS (COMME DASHBOARD PATIENT) ==========

// Styles pour les cartes de rendez-vous dans la vue patient
.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .appointment-card {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.15s ease;

    &:hover {
      border-color: #2563eb;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }

    &.urgent {
      border-color: #ef4444;
      box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.appointment-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.urgent-badge {
  font-size: 0.75rem;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  animation: pulse 2s infinite;
}

.appointment-date {
  font-size: 0.875rem;
  color: #4b5563;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;

  &.status-pending {
    background: #fef3c7;
    color: #92400e;
  }

  &.status-confirmed {
    background: #dbeafe;
    color: #1e40af;
  }

  &.status-assigned {
    background: #e0e7ff;
    color: #3730a3;
  }

  &.status-on-way {
    background: #fff7ed;
    color: #f57c00;
  }

  &.status-in-progress {
    background: #fef3c7;
    color: #92400e;
  }

  &.status-sampling-done {
    background: #d1fae5;
    color: #065f46;
  }

  &.status-analysis {
    background: #e0f2fe;
    color: #0c4a6e;
  }

  &.status-results {
    background: #dcfce7;
    color: #166534;
  }

  &.status-completed {
    background: #dcfce7;
    color: #166534;
  }

  &.status-cancelled {
    background: #fee2e2;
    color: #991b1b;
  }
}

.progress-section {
  padding: 1.5rem;
  background: white;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2563eb, #10b981);
  transition: width 0.3s ease;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  transition: all 0.15s ease;

  &.active {
    opacity: 1;
  }
}

.step-icon {
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.step-label {
  font-size: 0.75rem;
  text-align: center;
  color: #4b5563;
}

.card-body {
  padding: 1.5rem;
}

.appointment-details {
  .detail-item {
    display: flex;
    margin-bottom: 0.75rem;

    .detail-label {
      font-weight: 500;
      color: #374151;
      min-width: 120px;
      flex-shrink: 0;
    }

    .detail-value {
      color: #6b7280;
      flex: 1;
    }
  }
}

// Actions admin dans les cartes de rendez-vous
.admin-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;

  .action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }

    &.primary {
      background: #2563eb;
      color: white;
      border-color: #2563eb;

      &:hover {
        background: #1d4ed8;
      }
    }

    &.secondary {
      background: #f3f4f6;
      color: #374151;
      border-color: #d1d5db;

      &:hover {
        background: #e5e7eb;
      }
    }
  }
}

// ========== SECTION STATISTIQUES ==========

.statistics-section {
  h2 {
    color: #1f2937;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.stats-overview {
  margin-bottom: 3rem;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-card-large {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  h3 {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
  }

  .stat-total {
    font-size: 2rem;
    font-weight: bold;
    color: #2563eb;
  }
}

.stat-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    .stat-label {
      color: #6b7280;
      font-size: 0.875rem;
    }

    .stat-value {
      font-weight: 600;
      font-size: 0.875rem;

      &.pending { color: #f59e0b; }
      &.confirmed { color: #2563eb; }
      &.in-progress { color: #f57c00; }
      &.completed { color: #10b981; }
      &.active { color: #2563eb; }
      &.available { color: #10b981; }
      &.busy { color: #f59e0b; }
    }
  }
}

// Graphiques et métriques
.charts-section {
  margin-bottom: 3rem;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.chart-card, .metrics-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  h3 {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
  }
}

.pie-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  position: relative;
  background: #f9fafb;
  border-radius: 50%;
  font-size: 0.875rem;
  color: #6b7280;

  &::before {
    content: "Graphique\ndes statuts";
    text-align: center;
    white-space: pre-line;
  }
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.metric-item {
  .metric-label {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .metric-value {
    display: flex;
    align-items: center;
    gap: 1rem;

    .progress-bar {
      flex: 1;
      height: 8px;
      background: #e5e7eb;
      border-radius: 4px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #2563eb, #10b981);
        transition: width 0.3s ease;
      }
    }

    .metric-percentage {
      font-weight: 600;
      color: #374151;
      min-width: 3rem;
      text-align: right;
    }
  }
}

// Tableau récapitulatif
.summary-table {
  h3 {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
}

.table-container {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.stats-table {
  width: 100%;
  border-collapse: collapse;

  thead {
    background: #f9fafb;

    th {
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      color: #374151;
      border-bottom: 1px solid #e5e7eb;
    }
  }

  tbody {
    tr {
      &:nth-child(even) {
        background: #f9fafb;
      }

      &:hover {
        background: #f3f4f6;
      }

      td {
        padding: 1rem;
        color: #6b7280;
        border-bottom: 1px solid #e5e7eb;

        &:first-child {
          font-weight: 600;
          color: #374151;
        }

        &:last-child {
          font-weight: 600;
          color: #2563eb;
        }
      }
    }
  }
}

// ===== MODAL STYLES NETTOYÉS ET AMÉLIORÉS =====
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 20px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

.nurse-modal {
  background: white;
  border-radius: 24px;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 950px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  position: relative;
}

.nurse-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  z-index: 1;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 32px 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.modal-title-section {
  flex: 1;
  position: relative;
  z-index: 1;
}

.modal-title {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 800;
  display: flex;
  align-items: center;
  gap: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-icon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.modal-subtitle {
  margin: 0;
  font-size: 16px;
  opacity: 0.95;
  font-weight: 400;
  line-height: 1.5;
}

.close-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
  position: relative;
  z-index: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.close-icon {
  font-size: 20px;
  font-weight: bold;
  transition: transform 0.3s ease;
}

/* Appointment Summary Amélioré */
.appointment-summary {
  padding: 32px 40px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid #e2e8f0;
  position: relative;
}

.summary-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.summary-icon {
  font-size: 24px;
  color: #667eea;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2));
}

.summary-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.summary-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.summary-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.summary-row:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.summary-label {
  font-weight: 600;
  color: #64748b;
  min-width: 90px;
  font-size: 14px;
}

.summary-value {
  color: #1e293b;
  font-weight: 500;
  flex: 1;
}

.urgent-text {
  color: #dc2626;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2);
  animation: urgentPulse 2s infinite;
}

@keyframes urgentPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Nurses Section */
.nurses-section {
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 24px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f1f5f9;
}

.section-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-icon {
  font-size: 24px;
  color: #667eea;
}

.nurses-filter {
  position: relative;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 16px 20px 16px 52px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow:
    0 0 0 4px rgba(102, 126, 234, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #94a3b8;
  font-weight: 500;
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #64748b;
  transition: color 0.3s ease;
}

.search-input:focus + .search-icon {
  color: #667eea;
}

/* Nurses Grid */
.nurses-grid {
  display: grid;
  gap: 20px;
  max-height: 450px;
  overflow-y: auto;
  padding-right: 12px;
  scroll-behavior: smooth;
}

.nurses-grid::-webkit-scrollbar {
  width: 8px;
}

.nurses-grid::-webkit-scrollbar-track {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 4px;
}

.nurses-grid::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.nurses-grid::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.nurse-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.nurse-card:hover {
  border-color: #667eea;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.nurse-card.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  box-shadow:
    0 12px 32px rgba(102, 126, 234, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Nurse Avatar */
.nurse-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 20px;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 3px solid white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nurse-card:hover .avatar-circle {
  transform: scale(1.1);
  box-shadow:
    0 12px 32px rgba(102, 126, 234, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.availability-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
}

.indicator-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #ef4444;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.availability-indicator.available .indicator-dot {
  background: #10b981;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  animation: availablePulse 2s infinite;
}

@keyframes availablePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.5);
  }
}

/* Nurse Info */
.nurse-info {
  flex: 1;
  min-width: 0;
}

.nurse-name {
  margin: 0 0 6px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.nurse-email {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.nurse-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #64748b;
  font-weight: 600;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: translateY(-1px);
}

.stat-icon {
  font-size: 16px;
}

/* Nurse Actions */
.nurse-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.selection-indicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  animation: checkmark 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow:
    0 8px 25px rgba(16, 185, 129, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

@keyframes checkmark {
  from {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  to {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.availability-status {
  font-size: 12px;
  font-weight: 700;
  padding: 6px 12px;
  border-radius: 8px;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  border: 1px solid #fecaca;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
}

.availability-status.available {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  color: #16a34a;
  border-color: #bbf7d0;
}

/* Empty State */
.empty-nurses {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.7;
  filter: grayscale(0.3);
}

.empty-nurses h3 {
  margin: 0 0 12px 0;
  font-size: 22px;
  color: #1e293b;
  font-weight: 700;
}

.empty-nurses p {
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  max-width: 400px;
  margin: 0 auto;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 32px 40px;
  border-top: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.modal-actions .action-btn {
  min-width: 160px;
  padding: 16px 32px;
  font-weight: 700;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 15px;
  position: relative;
  overflow: hidden;
}

.modal-actions .action-btn.secondary {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #64748b;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-actions .action-btn.secondary:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-actions .action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

.modal-actions .action-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-3px);
  box-shadow:
    0 12px 32px rgba(102, 126, 234, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.15);
}

.modal-actions .action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.button-content .modal-icon {
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nurse-modal {
    width: 95%;
    max-height: 95vh;
    border-radius: 20px;
  }

  .modal-header,
  .appointment-summary,
  .nurses-section,
  .modal-actions {
    padding-left: 24px;
    padding-right: 24px;
  }

  .modal-header {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .modal-title {
    font-size: 24px;
  }

  .modal-icon {
    font-size: 28px;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .nurses-filter {
    min-width: auto;
  }

  .nurse-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 20px;
  }

  .nurse-stats {
    justify-content: center;
  }

  .modal-actions {
    flex-direction: column;
    gap: 12px;
  }

  .modal-actions .action-btn {
    width: 100%;
    min-width: auto;
  }

  .summary-content {
    grid-template-columns: 1fr;
  }

  .nurses-grid {
    max-height: 350px;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 10px;
  }

  .nurse-modal {
    width: 100%;
    max-height: 98vh;
    border-radius: 16px;
  }

  .modal-header,
  .appointment-summary,
  .nurses-section,
  .modal-actions {
    padding-left: 16px;
    padding-right: 16px;
  }

  .modal-title {
    font-size: 20px;
  }

  .search-input {
    padding: 14px 16px 14px 44px;
  }

  .nurse-card {
    padding: 16px;
  }

  .avatar-circle {
    width: 56px;
    height: 56px;
    font-size: 18px;
  }
}

// ========== STYLES POUR LA SECTION ANALYSES ==========

.analyses-section {
  .analyses-list-view {
    .analyses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 1.5rem;
      margin-top: 1.5rem;
    }

    .analysis-card {
      background: white;
      border-radius: 1rem;
      padding: 1.5rem;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
      border: 1px solid #e5e7eb;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.15);
        border-color: #1976d2;
      }

      &.inactive {
        opacity: 0.6;
        background: #f8f9fa;
      }

      .analysis-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;

        .analysis-info {
          flex: 1;

          .analysis-name {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 0.5rem 0;
          }

          .analysis-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
          }
        }

        .analysis-status {
          .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;

            &.active {
              background: #dcfce7;
              color: #166534;
            }

            &.inactive {
              background: #fef2f2;
              color: #dc2626;
            }
          }
        }
      }

      .analysis-details {
        margin-bottom: 1rem;

        .analysis-description {
          color: #6b7280;
          font-size: 0.875rem;
          line-height: 1.5;
          margin-bottom: 1rem;
        }

        .analysis-meta {
          display: flex;
          gap: 1rem;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .meta-label {
              font-size: 0.875rem;
              color: #6b7280;
            }

            .meta-value {
              font-size: 0.875rem;
              font-weight: 600;
              color: #1f2937;
            }
          }
        }
      }

      .analysis-actions {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
      }
    }
  }

  .add-analysis-form {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);

    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e5e7eb;

      .form-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .analysis-form {
      .form-row {
        margin-bottom: 1.5rem;

        &.two-columns {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
        }

        .full-width {
          width: 100%;
        }
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
      }
    }
  }

  .analysis-details-view {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);

    .details-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e5e7eb;

      .details-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .analysis-info-card {
      .info-section {
        margin-bottom: 2rem;

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;

          .info-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;

            .info-label {
              font-size: 0.875rem;
              color: #6b7280;
              font-weight: 500;
            }

            .info-value {
              font-size: 1rem;
              color: #1f2937;
              font-weight: 600;

              .status-badge {
                padding: 0.25rem 0.75rem;
                border-radius: 1rem;
                font-size: 0.875rem;

                &.active {
                  background: #dcfce7;
                  color: #166534;
                }

                &.inactive {
                  background: #fef2f2;
                  color: #dc2626;
                }
              }
            }
          }
        }

        .description-text {
          color: #6b7280;
          line-height: 1.6;
          font-size: 1rem;
        }
      }

      .actions-section {
        display: flex;
        gap: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 0.5rem;
    }

    p {
      color: #6b7280;
      margin-bottom: 2rem;
    }
  }
}

// Responsive pour les analyses
@media (max-width: 768px) {
  .analyses-section {
    .analyses-list-view {
      .analyses-grid {
        grid-template-columns: 1fr;
      }
    }

    .add-analysis-form {
      padding: 1rem;

      .analysis-form {
        .form-row.two-columns {
          grid-template-columns: 1fr;
        }

        .form-actions {
          flex-direction: column;
        }
      }
    }

    .analysis-details-view {
      padding: 1rem;

      .analysis-info-card {
        .info-section {
          .info-grid {
            grid-template-columns: 1fr;
          }
        }

        .actions-section {
          flex-direction: column;
        }
      }
    }
  }
}


