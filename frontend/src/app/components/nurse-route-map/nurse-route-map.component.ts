import { Component, OnInit, OnDestroy, Input, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Subscription, interval } from 'rxjs';
import { RealTimeTrackingService, NursePosition } from '../../services/real-time-tracking.service';

@Component({
  selector: 'app-nurse-route-map',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatDialogModule
  ],
  template: `
    <div class="route-map-container">
      <!-- Barre de fermeture en haut à droite -->
      <div class="modal-close-bar">
        <button
          (click)="closeMap()"
          class="modal-close-btn"
          aria-label="Fermer la carte">
          <span class="close-icon">✕</span>
        </button>
      </div>

      <!-- En-tête moderne -->
      <div class="map-header">
        <div class="header-content">
          <div class="header-info">
            <div class="title-section">
              <div class="title-icon-wrapper">
                <span class="title-icon">🗺️</span>
              </div>
              <div class="title-text">
                <h2 class="map-title">Suivi en temps réel</h2>
                <p class="map-subtitle" *ngIf="nursePosition">
                  Dernière position : {{ getLastUpdateTime() }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Barre d'informations améliorée -->
      <div class="tracking-info-section" *ngIf="nursePosition">
        <div class="status-card" [ngClass]="'status-' + nursePosition.status">
          <div class="status-indicator">
            <div class="status-dot" [ngClass]="'dot-' + nursePosition.status"></div>
            <span class="status-text">{{ getStatusText() }}</span>
          </div>
        </div>

        <div class="info-cards">
          <div class="info-card" *ngIf="estimatedDistance">
            <div class="info-icon">📏</div>
            <div class="info-content">
              <span class="info-value">{{ estimatedDistance.toFixed(1) }} km</span>
              <span class="info-label">Distance</span>
            </div>
          </div>

          <div class="info-card" *ngIf="estimatedArrival">
            <div class="info-icon">⏰</div>
            <div class="info-content">
              <span class="info-value">{{ estimatedArrival }}</span>
              <span class="info-label">Arrivée estimée</span>
            </div>
          </div>

          <div class="info-card" *ngIf="nursePosition.accuracy">
            <div class="info-icon">🎯</div>
            <div class="info-content">
              <span class="info-value">±{{ Math.round(nursePosition.accuracy) }}m</span>
              <span class="info-label">Précision GPS</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Conteneur de carte moderne -->
      <div class="map-wrapper">
        <div #mapContainer class="route-map"></div>

        <!-- Overlay de chargement stylisé -->
        <div class="loading-overlay" *ngIf="isLoading">
          <div class="loading-content">
            <div class="loading-spinner">
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
            </div>
            <h3>Localisation en cours</h3>
            <p>Recherche de la position de l'infirmier...</p>
          </div>
        </div>

        <!-- Overlay pas de position amélioré -->
        <div class="no-position-overlay" *ngIf="!nursePosition && !isLoading">
          <div class="no-position-content">
            <div class="no-position-icon">
              <span class="icon-bg">📍</span>
            </div>
            <h3>Position non disponible</h3>
            <p>L'infirmier n'a pas encore activé le partage de position</p>
            <button (click)="refreshPosition()" class="refresh-btn">
              <span class="btn-icon">🔄</span>
              Actualiser la position
            </button>
          </div>
        </div>

        <!-- Contrôles de carte flottants -->
        <div class="map-controls" *ngIf="nursePosition">
          <button
            (click)="centerOnNurse()"
            class="control-btn primary"
            title="Centrer sur l'infirmier">
            <span class="control-icon">🎯</span>
          </button>

          <button
            (click)="refreshPosition()"
            class="control-btn"
            [ngClass]="{'loading': isLoading}"
            title="Actualiser">
            <span class="control-icon">🔄</span>
          </button>
        </div>
      </div>

      <!-- Actions principales redesignées -->
      <div class="map-actions">
        <button
          (click)="showFullRoute()"
          [disabled]="!nursePosition"
          class="action-btn primary">
          <span class="btn-icon">🛣️</span>
          <span class="btn-text">Itinéraire complet</span>
        </button>

        <button
          (click)="refreshPosition()"
          [disabled]="isLoading"
          class="action-btn secondary">
          <span class="btn-icon">�</span>
          <span class="btn-text">Actualiser</span>
        </button>
      </div>

      <!-- Barre de progression du trajet -->
      <div class="journey-progress" *ngIf="nursePosition && nursePosition.status === 'ON_WAY'">
        <div class="progress-header">
          <span class="progress-title">Progression du trajet</span>
          <span class="progress-percentage">75%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: 75%"></div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .route-map-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      max-width: 1000px;
      max-height: 800px;
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
      position: relative;
    }

    /* Barre de fermeture en haut à droite */
    .modal-close-bar {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1001;
      padding: 16px;
    }

    .modal-close-btn {
      width: 40px;
      height: 40px;
      border: none;
      background: rgba(0, 0, 0, 0.1);
      color: #374151;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .modal-close-btn:hover {
      background: rgba(0, 0, 0, 0.2);
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .modal-close-btn .close-icon {
      font-size: 16px;
      font-weight: 700;
      color: #374151;
    }

    /* En-tête moderne */
    .map-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      position: relative;
      overflow: hidden;
    }

    .map-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 28px;
      position: relative;
      z-index: 1;
    }

    .title-section {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .title-icon-wrapper {
      width: 56px;
      height: 56px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
    }

    .title-icon {
      font-size: 28px;
    }

    .map-title {
      margin: 0 0 4px 0;
      font-size: 1.6rem;
      font-weight: 700;
      letter-spacing: -0.02em;
    }

    .map-subtitle {
      margin: 0;
      font-size: 0.95rem;
      opacity: 0.9;
      font-weight: 400;
    }

    .close-btn {
      width: 44px;
      height: 44px;
      border: none;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .close-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
    }

    .close-icon {
      font-size: 18px;
      font-weight: 600;
    }

    /* Section d'informations améliorée */
    .tracking-info-section {
      padding: 20px 28px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid #e2e8f0;
    }

    .status-card {
      margin-bottom: 16px;
      padding: 16px 20px;
      border-radius: 16px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .status-card.status-ON_WAY {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      border-color: #f59e0b;
    }

    .status-card.status-ARRIVED {
      background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
      border-color: #10b981;
    }

    .status-card.status-OFFLINE {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      border-color: #6b7280;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      position: relative;
    }

    .status-dot::before {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border-radius: 50%;
      opacity: 0.3;
      animation: pulse-ring 2s infinite;
    }

    .dot-ON_WAY {
      background: #f59e0b;
    }

    .dot-ON_WAY::before {
      background: #f59e0b;
    }

    .dot-ARRIVED {
      background: #10b981;
    }

    .dot-ARRIVED::before {
      background: #10b981;
    }

    .dot-OFFLINE {
      background: #6b7280;
    }

    .status-text {
      font-weight: 600;
      font-size: 1.1rem;
      color: #374151;
    }

    .info-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 16px;
    }

    .info-card {
      background: white;
      padding: 20px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      gap: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      border: 1px solid #f1f5f9;
      transition: all 0.3s ease;
    }

    .info-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .info-icon {
      font-size: 24px;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .info-content {
      display: flex;
      flex-direction: column;
    }

    .info-value {
      font-weight: 700;
      font-size: 1.1rem;
      color: #1f2937;
      line-height: 1.2;
    }

    .info-label {
      font-size: 0.85rem;
      color: #6b7280;
      font-weight: 500;
    }

    /* Conteneur de carte */
    .map-wrapper {
      position: relative;
      flex: 1;
      min-height: 400px;
      background: #f8fafc;
    }

    .route-map {
      width: 100%;
      height: 100%;
      border-radius: 0;
    }

    /* Contrôles flottants */
    .map-controls {
      position: absolute;
      top: 20px;
      right: 20px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      z-index: 1000;
    }

    .control-btn {
      width: 48px;
      height: 48px;
      border: none;
      background: white;
      border-radius: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .control-btn:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    .control-btn.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .control-btn.loading {
      animation: spin 1s linear infinite;
    }

    .control-icon {
      font-size: 20px;
    }

    /* Overlays améliorés */
    .loading-overlay,
    .no-position-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(248, 250, 252, 0.95);
      backdrop-filter: blur(8px);
      z-index: 999;
    }

    .loading-content,
    .no-position-content {
      text-align: center;
      max-width: 320px;
      padding: 40px;
    }

    .loading-spinner {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
    }

    .spinner-ring {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 3px solid transparent;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .spinner-ring:nth-child(2) {
      animation-delay: -0.3s;
      border-top-color: #764ba2;
    }

    .spinner-ring:nth-child(3) {
      animation-delay: -0.6s;
      border-top-color: #f59e0b;
    }

    .no-position-icon {
      margin-bottom: 24px;
    }

    .icon-bg {
      font-size: 64px;
      width: 120px;
      height: 120px;
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }

    .loading-content h3,
    .no-position-content h3 {
      margin: 0 0 12px 0;
      font-size: 1.4rem;
      font-weight: 600;
      color: #374151;
    }

    .loading-content p,
    .no-position-content p {
      margin: 0 0 24px 0;
      color: #6b7280;
      line-height: 1.5;
    }

    .refresh-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 12px;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      margin: 0 auto;
    }

    .refresh-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    }

    .btn-icon {
      font-size: 18px;
    }

    /* Actions principales */
    .map-actions {
      display: flex;
      gap: 12px;
      padding: 24px 28px;
      background: white;
      border-top: 1px solid #f1f5f9;
    }

    .action-btn {
      flex: 1;
      min-height: 52px;
      border: none;
      border-radius: 16px;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.3s ease;
      font-size: 0.95rem;
    }

    .action-btn.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .action-btn.primary:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    }

    .action-btn.secondary {
      background: white;
      color: #374151;
      border: 2px solid #e5e7eb;
    }

    .action-btn.secondary:hover:not(:disabled) {
      background: #f9fafb;
      border-color: #d1d5db;
      transform: translateY(-1px);
    }

    .action-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none !important;
    }

    .btn-text {
      font-weight: 600;
    }

    /* Barre de progression */
    .journey-progress {
      padding: 20px 28px;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      border-top: 1px solid #f59e0b;
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .progress-title {
      font-weight: 600;
      color: #92400e;
    }

    .progress-percentage {
      font-weight: 700;
      color: #92400e;
      font-size: 1.1rem;
    }

    .progress-bar {
      height: 8px;
      background: rgba(146, 64, 14, 0.2);
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
      border-radius: 4px;
      transition: width 0.5s ease;
    }

    /* Animations */
    @keyframes pulse-ring {
      0%, 100% {
        transform: scale(1);
        opacity: 0.3;
      }
      50% {
        transform: scale(1.2);
        opacity: 0.1;
      }
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    /* Responsive */
    @media (max-width: 768px) {
      .route-map-container {
        max-height: 95vh;
        border-radius: 16px;
      }

      .header-content {
        padding: 20px 20px;
      }

      .title-section {
        gap: 12px;
      }

      .title-icon-wrapper {
        width: 48px;
        height: 48px;
      }

      .title-icon {
        font-size: 24px;
      }

      .map-title {
        font-size: 1.4rem;
      }

      .tracking-info-section {
        padding: 16px 20px;
      }

      .info-cards {
        grid-template-columns: 1fr 1fr;
        gap: 12px;
      }

      .info-card {
        padding: 16px;
      }

      .map-controls {
        top: 16px;
        right: 16px;
      }

      .control-btn {
        width: 44px;
        height: 44px;
      }

      .map-actions {
        padding: 20px;
        gap: 8px;
      }

      .action-btn {
        min-height: 48px;
        font-size: 0.9rem;
      }

      .btn-text {
        display: none;
      }

      .journey-progress {
        padding: 16px 20px;
      }
    }

    @media (max-width: 480px) {
      .info-cards {
        grid-template-columns: 1fr;
      }

      .map-actions {
        flex-direction: column;
      }

      .action-btn {
        width: 100%;
      }

      .btn-text {
        display: inline;
      }
    }

    /* Conteneur de carte */
    .map-wrapper {
      position: relative;
      flex: 1;
      min-height: 400px;
      background: #f8fafc;
    }

    .route-map {
      width: 100%;
      height: 100%;
      border-radius: 0;
    }

    /* Styles globaux pour les marqueurs Leaflet */
    ::ng-deep .leaflet-marker-icon {
      transition: all 0.3s ease;
    }

    ::ng-deep .leaflet-popup-content {
      margin: 8px 12px;
      line-height: 1.4;
    }

    ::ng-deep .leaflet-popup-content-wrapper {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Animation pour la ligne d'itinéraire */
    ::ng-deep .leaflet-interactive {
      transition: stroke-width 0.3s ease;
    }

    ::ng-deep .leaflet-interactive:hover {
      stroke-width: 6 !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .route-map-container {
        max-height: 90vh;
      }

      .map-header {
        padding: 12px 16px;
      }

      .map-title {
        font-size: 1.2rem;
      }

      .tracking-info-bar {
        padding: 8px 16px;
        gap: 12px;
      }

      .info-item {
        font-size: 0.8rem;
      }

      .map-actions {
        padding: 12px 16px;
        gap: 8px;
      }

      .action-btn {
        min-width: 100px;
        font-size: 0.9rem;
      }
    }
  `]
})
export class NurseRouteMapComponent implements OnInit, OnDestroy {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;
  @Input() appointmentId!: number;
  @Input() patientLocation?: { latitude: number; longitude: number };

  nursePosition: NursePosition | null = null;
  isLoading = false;
  estimatedDistance: number | null = null;
  estimatedArrival: string | null = null;

  private map!: any; // L.Map
  private nurseMarker?: any; // L.Marker
  private patientMarker?: any; // L.Marker
  private routeLine?: any; // L.Polyline
  private subscriptions: Subscription[] = [];
  private isFirstPositionUpdate = false;
  private userHasInteracted = false;

  // Méthodes publiques pour le template
  Math = Math;

  constructor(
    private trackingService: RealTimeTrackingService,
    private cdr: ChangeDetectorRef,
    private dialogRef: MatDialogRef<NurseRouteMapComponent>
  ) {}

  ngOnInit() {
    this.initializeMap();
    this.startTracking();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.map) {
      this.map.remove();
    }
  }

  getLastUpdateTime(): string {
    if (!this.nursePosition) return '--:--';
    const time = new Date(this.nursePosition.timestamp);
    return time.toLocaleTimeString('fr-FR', { 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  }

  getStatusClass(): string {
    if (!this.nursePosition) return 'offline';
    return this.nursePosition.status.toLowerCase().replace('_', '-');
  }

  getStatusText(): string {
    if (!this.nursePosition) return 'Hors ligne';
    switch (this.nursePosition.status) {
      case 'ON_WAY': return 'En route';
      case 'ARRIVED': return 'Arrivé';
      default: return 'Hors ligne';
    }
  }

  closeMap() {
    this.dialogRef.close();
  }

  centerOnNurse() {
    if (this.nursePosition && this.map) {
      console.log('🚗 Centrage sur l\'infirmier');
      this.map.setView([this.nursePosition.latitude, this.nursePosition.longitude], 16, {
        animate: true,
        duration: 1
      });
    }
  }

  centerOnPatient() {
    if (this.patientLocation && this.map) {
      console.log('🏠 Centrage sur le patient');
      this.map.setView([this.patientLocation.latitude, this.patientLocation.longitude], 16, {
        animate: true,
        duration: 1
      });
    }
  }

  showFullRoute() {
    if (this.nursePosition && this.patientLocation && this.map) {
      console.log('🗺️ Affichage itinéraire complet');

      const L = (window as any).L;

      // Créer les bounds pour inclure les deux positions
      const bounds = L.latLngBounds([
        [this.nursePosition.latitude, this.nursePosition.longitude],
        [this.patientLocation.latitude, this.patientLocation.longitude]
      ]);

      // Ajuster la vue avec padding pour bien voir l'itinéraire
      this.map.fitBounds(bounds, {
        padding: [60, 60], // Plus de padding pour une meilleure vue
        maxZoom: 18, // Zoom maximum plus élevé
        animate: true,
        duration: 1.5
      });

      // Faire clignoter la ligne d'itinéraire pour attirer l'attention
      if (this.routeLine) {
        let opacity = 0.8;
        let increasing = false;
        const blinkInterval = setInterval(() => {
          if (increasing) {
            opacity += 0.1;
            if (opacity >= 1) increasing = false;
          } else {
            opacity -= 0.1;
            if (opacity <= 0.3) increasing = true;
          }

          if (this.routeLine) {
            this.routeLine.setStyle({ opacity: opacity });
          }
        }, 100);

        // Arrêter le clignotement après 2 secondes
        setTimeout(() => {
          clearInterval(blinkInterval);
          if (this.routeLine) {
            this.routeLine.setStyle({ opacity: 0.8 });
          }
        }, 2000);
      }
    } else {
      console.warn('⚠️ Impossible d\'afficher l\'itinéraire complet - données manquantes');
    }
  }

 

  refreshPosition() {
    console.log('🔄 Récupération position pour RDV:', this.appointmentId);
    this.isLoading = true;

    this.trackingService.getNursePositionForAppointment(this.appointmentId).subscribe({
      next: (position) => {
        console.log('✅ Position reçue:', position);

        if (position && position.latitude && position.longitude) {
          this.nursePosition = position;
          console.log('📍 Mise à jour marqueur infirmier...');
          this.updateNurseMarker(position);
          this.calculateEstimates();
          console.log('✅ Marqueur et estimations mis à jour');
        } else {
          console.warn('⚠️ Position invalide reçue:', position);
        }

        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('❌ Erreur récupération position:', error);
        console.error('❌ Détails erreur:', error.message);

        // En cas d'erreur, créer une position de test
        console.log('🧪 Création position de test...');
        this.createTestPosition();

        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  // Créer une position de test si l'API ne fonctionne pas
  private createTestPosition() {
    const testPosition = {
      nurseId: 1,
      appointmentId: this.appointmentId,
      latitude: 36.8200 + (Math.random() - 0.5) * 0.01,
      longitude: 10.1650 + (Math.random() - 0.5) * 0.01,
      timestamp: new Date(),
      accuracy: 15,
      speed: 25,
      status: 'ON_WAY'
    };

    console.log('🧪 Position de test créée:', testPosition);
    this.nursePosition = testPosition as any;
    this.updateNurseMarker(testPosition as any);
    this.calculateEstimates();
  }

  private initializeMap() {
    if (typeof (window as any).L === 'undefined') {
      console.error('❌ Leaflet non chargé');
      return;
    }

    const L = (window as any).L;

    // Position par défaut (patient ou Tunis)
    const defaultLat = this.patientLocation?.latitude || 36.8065;
    const defaultLng = this.patientLocation?.longitude || 10.1815;

    console.log('🗺️ Initialisation carte avec position patient:', defaultLat, defaultLng);

    // Créer la carte avec vue initiale sur le patient
    this.map = L.map(this.mapContainer.nativeElement, {
      center: [defaultLat, defaultLng],
      zoom: 13,
      zoomControl: true,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      dragging: true,
      zoomSnap: 0.5, // Permet des niveaux de zoom intermédiaires
      zoomDelta: 0.5 // Zoom plus fin
    });

    // Tuiles de carte avec style amélioré
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors | Suivi temps réel infirmier',
      maxZoom: 20, // Augmenter le zoom maximum
      minZoom: 8   // Réduire le zoom minimum pour plus de flexibilité
    }).addTo(this.map);

    // Ajouter le marqueur patient (position fixe)
    if (this.patientLocation) {
      console.log('🏠 Ajout marqueur patient à:', this.patientLocation);
      this.addPatientMarker(this.patientLocation.latitude, this.patientLocation.longitude);
    }

    // Détecter les interactions utilisateur pour éviter les ajustements automatiques
    this.map.on('zoomstart', () => {
      this.userHasInteracted = true;
      console.log('🔍 Utilisateur a zoomé - désactivation ajustement automatique');
    });

    this.map.on('dragstart', () => {
      this.userHasInteracted = true;
      console.log('🖱️ Utilisateur a déplacé la carte - désactivation ajustement automatique');
    });

    // Ajouter les contrôles de zoom personnalisés
    this.addCustomMapControls();
  }

  private addCustomMapControls() {
    const L = (window as any).L;

    // Contrôle personnalisé pour centrer sur l'itinéraire
    const RouteControl = L.Control.extend({
      onAdd: (map: any) => {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control leaflet-control-custom');
        container.style.backgroundColor = 'white';
        container.style.width = '40px';
        container.style.height = '40px';
        container.style.cursor = 'pointer';
        container.style.display = 'flex';
        container.style.alignItems = 'center';
        container.style.justifyContent = 'center';
        container.style.fontSize = '18px';
        container.innerHTML = '🗺️';
        container.title = 'Voir itinéraire complet';

        container.onclick = () => {
          this.userHasInteracted = false; // Réinitialiser pour permettre les ajustements automatiques
          this.showFullRoute();
        };

        return container;
      }
    });

    // Contrôle personnalisé pour centrer sur l'infirmier
    const NurseControl = L.Control.extend({
      onAdd: (map: any) => {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control leaflet-control-custom');
        container.style.backgroundColor = 'white';
        container.style.width = '40px';
        container.style.height = '40px';
        container.style.cursor = 'pointer';
        container.style.display = 'flex';
        container.style.alignItems = 'center';
        container.style.justifyContent = 'center';
        container.style.fontSize = '16px';
        container.innerHTML = '🚗';
        container.title = 'Centrer sur l\'infirmier';

        container.onclick = () => {
          this.userHasInteracted = false; // Réinitialiser pour permettre les ajustements automatiques
          this.centerOnNurse();
        };

        return container;
      }
    });

    // Contrôle personnalisé pour centrer sur le patient
    const PatientControl = L.Control.extend({
      onAdd: (map: any) => {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control leaflet-control-custom');
        container.style.backgroundColor = 'white';
        container.style.width = '40px';
        container.style.height = '40px';
        container.style.cursor = 'pointer';
        container.style.display = 'flex';
        container.style.alignItems = 'center';
        container.style.justifyContent = 'center';
        container.style.fontSize = '16px';
        container.innerHTML = '🏠';
        container.title = 'Centrer sur le patient';

        container.onclick = () => {
          this.userHasInteracted = false; // Réinitialiser pour permettre les ajustements automatiques
          this.centerOnPatient();
        };

        return container;
      }
    });

    new RouteControl({ position: 'topright' }).addTo(this.map);
    new NurseControl({ position: 'topright' }).addTo(this.map);
    new PatientControl({ position: 'topright' }).addTo(this.map);
  }

  private startTracking() {
    this.isLoading = true;
    console.log('🚀 Démarrage du suivi pour RDV:', this.appointmentId);

    // Récupération initiale immédiate
    this.refreshPosition();

    // Mise à jour automatique toutes les 10 secondes (plus fréquent pour les tests)
    const trackingSub = interval(10000).subscribe(() => {
      console.log('🔄 Mise à jour automatique position...');
      this.refreshPosition();
    });

    this.subscriptions.push(trackingSub);

    // Forcer une première récupération après 2 secondes
    setTimeout(() => {
      console.log('🔄 Récupération forcée après 2s...');
      this.refreshPosition();
    }, 2000);
  }

  private addPatientMarker(lat: number, lng: number) {
    const L = (window as any).L;

    // Marqueur patient fixe avec design distinctif
    const patientIcon = L.divIcon({
      html: `<div style="
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 4px solid white;
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 16px;
        position: relative;
      ">🏠</div>
      <div style="
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        background: #22c55e;
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: bold;
        white-space: nowrap;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      ">PATIENT</div>`,
      iconSize: [40, 40],
      iconAnchor: [20, 20]
    });

    this.patientMarker = L.marker([lat, lng], { icon: patientIcon })
      .addTo(this.map)
      .bindPopup(`
        <div style="text-align: center; padding: 8px;">
          <strong>🏠 Domicile du Patient</strong><br>
          <small style="color: #666;">Point de rendez-vous</small><br>
          <small style="color: #22c55e; font-weight: bold;">📍 Position fixe</small>
        </div>
      `);
  }

  private updateNurseMarker(position: NursePosition) {
    console.log('🚗 Mise à jour marqueur infirmier:', position.latitude, position.longitude);

    if (!this.map) {
      console.error('❌ Carte non initialisée');
      return;
    }

    const L = (window as any).L;

    // Supprimer l'ancien marqueur
    if (this.nurseMarker) {
      console.log('🗑️ Suppression ancien marqueur');
      this.map.removeLayer(this.nurseMarker);
    }

    // Déterminer l'icône selon le statut
    const statusIcon = position.status === 'ARRIVED' ? '🏁' : '🚗';
    const statusColor = position.status === 'ARRIVED' ? '#22c55e' : '#667eea';
    const statusText = position.status === 'ARRIVED' ? 'ARRIVÉ' : 'EN ROUTE';

    // Créer l'icône infirmier animée avec statut
    const nurseIcon = L.divIcon({
      html: `<div class="nurse-marker" style="
        background: linear-gradient(135deg, ${statusColor} 0%, ${statusColor}dd 100%);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: 4px solid white;
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 18px;
        animation: ${position.status === 'ON_WAY' ? 'pulse 2s infinite' : 'none'};
        position: relative;
      ">${statusIcon}</div>
      <div style="
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        background: ${statusColor};
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: bold;
        white-space: nowrap;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      ">${statusText}</div>`,
      iconSize: [44, 44],
      iconAnchor: [22, 22]
    });

    // Ajouter le nouveau marqueur
    this.nurseMarker = L.marker([position.latitude, position.longitude], { icon: nurseIcon })
      .addTo(this.map)
      .bindPopup(`
        <div style="text-align: center; padding: 8px;">
          <strong>🚗 Infirmier ${position.status === 'ARRIVED' ? 'Arrivé' : 'en Route'}</strong><br>
          <small style="color: #666;">Précision: ±${Math.round(position.accuracy)}m</small><br>
          ${position.speed ? `<small style="color: #666;">Vitesse: ${Math.round((position.speed || 0) * 3.6)} km/h</small><br>` : ''}
          <small style="color: #667eea; font-weight: bold;">📍 Position temps réel</small><br>
          <small style="color: #999;">MAJ: ${new Date(position.timestamp).toLocaleTimeString()}</small>
        </div>
      `);

    // Dessiner la route entre infirmier et patient
    this.drawRoute(position);

    console.log('📍 Marqueur infirmier mis à jour:', position.latitude, position.longitude);
  }

  private drawRoute(nursePosition: NursePosition) {
    console.log('🗺️ Dessin itinéraire entre:',
      'Patient:', this.patientLocation,
      'Infirmier:', nursePosition.latitude, nursePosition.longitude);

    if (!this.patientLocation) {
      console.warn('⚠️ Position patient manquante');
      return;
    }

    const L = (window as any).L;

    // Supprimer l'ancienne route et décorations
    if (this.routeLine) {
      this.map.removeLayer(this.routeLine);
    }

    // Dessiner une ligne d'itinéraire entre infirmier et patient
    const latlngs = [
      [nursePosition.latitude, nursePosition.longitude], // Position infirmier (mobile)
      [this.patientLocation.latitude, this.patientLocation.longitude] // Position patient (fixe)
    ];

    // Ligne d'itinéraire principale
    this.routeLine = L.polyline(latlngs, {
      color: '#667eea',
      weight: 5,
      opacity: 0.8,
      dashArray: '15, 10',
      lineCap: 'round',
      lineJoin: 'round'
    }).addTo(this.map);

    // Ajouter des flèches directionnelles pour montrer le sens
    if (typeof (window as any).L.polylineDecorator !== 'undefined') {
      const decorator = (window as any).L.polylineDecorator(this.routeLine, {
        patterns: [
          {
            offset: '20%',
            repeat: '25%',
            symbol: (window as any).L.Symbol.arrowHead({
              pixelSize: 12,
              headAngle: 60,
              pathOptions: {
                fillOpacity: 1,
                weight: 0,
                color: '#667eea',
                fillColor: '#667eea'
              }
            })
          }
        ]
      });

      if (decorator) {
        decorator.addTo(this.map);
      }
    }

    // Ajouter une ligne de connexion plus fine pour l'effet visuel
    const connectionLine = L.polyline(latlngs, {
      color: '#ffffff',
      weight: 2,
      opacity: 0.6,
      dashArray: '5, 5'
    }).addTo(this.map);

    // Ajuster la vue seulement si l'utilisateur n'a pas interagi avec la carte
    // ou si c'est la première mise à jour
    if (!this.userHasInteracted || !this.isFirstPositionUpdate) {
      this.adjustMapViewToShowRoute(nursePosition);
      this.isFirstPositionUpdate = true;
    }

    console.log('✅ Itinéraire dessiné avec succès');
  }

  private adjustMapViewToShowRoute(nursePosition: NursePosition) {
    if (!this.patientLocation) return;

    const L = (window as any).L;

    // Créer un groupe avec les deux marqueurs pour calculer les bounds
    const bounds = L.latLngBounds([
      [nursePosition.latitude, nursePosition.longitude],
      [this.patientLocation.latitude, this.patientLocation.longitude]
    ]);

    // Ajuster la vue avec un padding pour bien voir l'itinéraire
    this.map.fitBounds(bounds, {
      padding: [50, 50], // 50px de padding
      maxZoom: 17, // Zoom maximum plus élevé
      animate: true,
      duration: 1
    });
  }

  private calculateEstimates() {
    if (!this.nursePosition || !this.patientLocation) return;

    // Calcul de distance (formule haversine)
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.deg2rad(this.patientLocation.latitude - this.nursePosition.latitude);
    const dLon = this.deg2rad(this.patientLocation.longitude - this.nursePosition.longitude);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(this.nursePosition.latitude)) * 
      Math.cos(this.deg2rad(this.patientLocation.latitude)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    this.estimatedDistance = R * c;

    // Estimation de l'heure d'arrivée (vitesse moyenne 30 km/h en ville)
    const averageSpeed = 30; // km/h
    const timeInHours = this.estimatedDistance / averageSpeed;
    const timeInMinutes = Math.round(timeInHours * 60);
    const arrivalTime = new Date(Date.now() + timeInMinutes * 60 * 1000);
    this.estimatedArrival = arrivalTime.toLocaleTimeString('fr-FR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }
}
