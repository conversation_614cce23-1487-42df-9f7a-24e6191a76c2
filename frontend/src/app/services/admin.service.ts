import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { Appointment, AppointmentStatus } from '../models/appointment.model';
import { User } from '../models/user.model';

export interface AutoAssignResult {
  totalPending: number;
  assigned: number;
  failed: number;
  errors: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private apiUrl = `${environment.apiUrl}/appointments/admin`;

  constructor(private http: HttpClient) {}

  // ========== GESTION DES PATIENTS ==========

  /**
   * Récupère tous les patients
   */
  getAllPatients(): Observable<User[]> {
    return this.http.get<User[]>(`${environment.apiUrl}/admin/patients`);
  }

  /**
   * Récupère les rendez-vous d'un patient spécifique
   */
  getPatientAppointments(patientId: number): Observable<Appointment[]> {
    return this.http.get<Appointment[]>(`${environment.apiUrl}/admin/patients/${patientId}/appointments`);
  }

  /**
   * Récupère un patient par ID
   */
  getPatientById(id: number): Observable<User> {
    return this.http.get<User>(`${environment.apiUrl}/admin/patients/${id}`);
  }

  // ========== GESTION DES INFIRMIERS ==========

  /**
   * Récupère tous les infirmiers
   */
  getAllNurses(): Observable<User[]> {
    return this.http.get<User[]>(`${environment.apiUrl}/admin/nurses`);
  }

  /**
   * Récupère un infirmier par ID
   */
  getNurseById(id: number): Observable<User> {
    return this.http.get<User>(`${environment.apiUrl}/admin/nurses/${id}`);
  }

  /**
   * Test de connectivité avec l'endpoint nurses
   */
  testNursesEndpoint(): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/admin/nurses/test`, {});
  }

  /**
   * Crée un nouvel infirmier
   */
  createNurse(nurseData: any): Observable<User> {
    return this.http.post<User>(`${environment.apiUrl}/admin/nurses`, nurseData);
  }

  /**
   * Met à jour un infirmier
   */
  updateNurse(id: number, nurseData: any): Observable<User> {
    return this.http.put<User>(`${environment.apiUrl}/admin/nurses/${id}`, nurseData);
  }

  /**
   * Supprime un infirmier
   */
  deleteNurse(id: number): Observable<void> {
    return this.http.delete<void>(`${environment.apiUrl}/admin/nurses/${id}`);
  }

  /**
   * Active/Désactive un infirmier
   */
  toggleNurseStatus(id: number, enabled: boolean): Observable<User> {
    return this.http.patch<User>(`${environment.apiUrl}/admin/nurses/${id}/status`, { enabled });
  }

  /**
   * Met à jour la disponibilité d'un infirmier
   */
  updateNurseAvailability(id: number, isAvailable: boolean): Observable<User> {
    return this.http.patch<User>(`${environment.apiUrl}/admin/nurses/${id}/availability`, { isAvailable });
  }

  /**
   * Recherche des infirmiers avec filtres
   */
  searchNurses(searchTerm?: string, status?: string, availability?: string): Observable<User[]> {
    let params = new HttpParams();

    if (searchTerm) {
      params = params.set('search', searchTerm);
    }
    if (status && status !== 'all') {
      params = params.set('status', status);
    }
    if (availability && availability !== 'all') {
      params = params.set('availability', availability);
    }

    return this.http.get<User[]>(`${environment.apiUrl}/admin/nurses/search`, { params });
  }

  /**
   * Récupère les statistiques des infirmiers
   */
  getNurseStatistics(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/admin/nurses/statistics`);
  }

  // ========== GESTION DES RENDEZ-VOUS ==========

  /**
   * Récupère tous les rendez-vous
   */
  getAllAppointments(): Observable<Appointment[]> {
    return this.http.get<Appointment[]>(`${this.apiUrl}/all`);
  }

  /**
   * Récupère les rendez-vous en attente
   */
  getPendingAppointments(): Observable<Appointment[]> {
    return this.http.get<Appointment[]>(`${this.apiUrl}/pending`);
  }

  /**
   * Affecte manuellement un infirmier à un rendez-vous
   */
  assignNurseToAppointment(appointmentId: number, nurseId: number): Observable<Appointment> {
    return this.http.post<Appointment>(`${this.apiUrl}/${appointmentId}/assign-nurse/${nurseId}`, {});
  }

  /**
   * Affecte automatiquement l'infirmier le plus proche
   */
  autoAssignNearestNurse(appointmentId: number): Observable<Appointment> {
    return this.http.post<Appointment>(`${this.apiUrl}/${appointmentId}/auto-assign`, {});
  }

  /**
   * Affecte automatiquement tous les rendez-vous en attente
   */
  autoAssignAllPendingAppointments(): Observable<AutoAssignResult> {
    return this.http.post<AutoAssignResult>(`${this.apiUrl}/auto-assign-all`, {});
  }

  /**
   * Met à jour le statut d'un rendez-vous
   */
  updateAppointmentStatus(appointmentId: number, status: AppointmentStatus): Observable<Appointment> {
    const params = new HttpParams().set('status', status);
    return this.http.put<Appointment>(`${this.apiUrl}/${appointmentId}/status`, {}, { params });
  }

  /**
   * Récupère la liste des infirmiers disponibles
   */
  getAvailableNurses(): Observable<User[]> {
    return this.http.get<User[]>(`${this.apiUrl}/nurses/available`);
  }

  // ========== STATISTIQUES ==========

  /**
   * Récupère les statistiques des rendez-vous
   */
  getAppointmentStats(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/admin/stats/appointments`);
  }

  /**
   * Récupère les statistiques des infirmiers
   */
  getNurseStats(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/admin/stats/nurses`);
  }

  // ========== GESTION DES ANALYSES ==========

  /**
   * Récupère toutes les analyses
   */
  getAllAnalyses(): Observable<any[]> {
    return this.http.get<any[]>(`${environment.apiUrl}/admin/analyses`);
  }

  /**
   * Crée une nouvelle analyse
   */
  createAnalysis(analysisData: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/admin/analyses`, analysisData);
  }

  /**
   * Met à jour une analyse
   */
  updateAnalysis(id: number, analysisData: any): Observable<any> {
    return this.http.put<any>(`${environment.apiUrl}/admin/analyses/${id}`, analysisData);
  }

  /**
   * Supprime une analyse
   */
  deleteAnalysis(id: number): Observable<any> {
    return this.http.delete<any>(`${environment.apiUrl}/admin/analyses/${id}`);
  }

  /**
   * Active/désactive une analyse
   */
  toggleAnalysisStatus(id: number, isActive: boolean): Observable<any> {
    return this.http.patch<any>(`${environment.apiUrl}/admin/analyses/${id}/status`, { isActive });
  }
}
